<template>
  <div class="app-container">
    <div class="update-container">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span class="card-title">系统更新</span>
        </div>

        <div class="update-content">
          <div class="update-info">
            <el-alert
              title="系统更新说明"
              type="info"
              :closable="false"
              description="请选择系统更新包（ZIP格式）进行上传"
              show-icon>
            </el-alert>
          </div>

          <div class="upload-section">
            <el-upload
              ref="upload"
              class="upload-demo"
              :action="uploadUrl"
              :on-success="handleSuccess"
              :on-error="handleError"
              :before-upload="beforeUpload"
              :on-change="handleFileChange"
              :on-progress="handleProgress"
              :show-file-list="false"
              :auto-upload="false"
              accept=".zip">
              <el-button
                slot="trigger"
                size="small"
                type="primary"
                icon="el-icon-folder-opened"
                :disabled="selectedFile && !uploading">
                {{ selectedFile ? '已选择文件' : '选择更新包' }}
              </el-button>
              <el-button
                style="margin-left: 10px;"
                size="small"
                type="success"
                icon="el-icon-upload2"
                :loading="uploading"
                :disabled="!selectedFile"
                @click="submitUpload">
                {{ uploading ? '上传中...' : '开始更新' }}
              </el-button>
            </el-upload>

            <div v-if="selectedFile" class="file-info">
              <p><strong>已选择文件：</strong>{{ selectedFile.name }}</p>
              <p><strong>文件大小：</strong>{{ formatFileSize(selectedFile.size) }}</p>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-refresh-left"
                @click="resetFileSelection">
                重新选择
              </el-button>
            </div>

            <div v-if="uploading" class="progress-section">
              <el-progress
                :percentage="uploadProgress"
                :status="uploadProgress === 100 ? 'success' : ''"
                :stroke-width="8">
              </el-progress>
              <p class="progress-text">正在上传更新包，请稍候...</p>
            </div>
          </div>

          <div class="current-version">
            <el-divider content-position="left">当前版本信息</el-divider>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="系统版本">{{ systemInfo.version || 'v1.0.0' }}</el-descriptions-item>
              <el-descriptions-item label="构建时间">{{ systemInfo.buildTime || '2024-01-01 12:00:00' }}</el-descriptions-item>
              <el-descriptions-item label="运行环境">{{ systemInfo.environment || 'Production' }}</el-descriptions-item>
              <el-descriptions-item label="最后更新">{{ systemInfo.lastUpdate || '2024-01-01 12:00:00' }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'SystemUpdate',
  data() {
    return {
      uploadUrl: '/api/sys/update/upload', // 后端上传接口
      uploading: false,
      uploadProgress: 0,
      selectedFile: null,
      systemInfo: {
        version: 'v1.0.0',
        buildTime: '2024-01-01 12:00:00',
        environment: 'Production',
        lastUpdate: '2024-01-01 12:00:00'
      }
    }
  },
  mounted() {
    // this.getSystemInfo()
  },
  methods: {
    // 获取系统信息
    async getSystemInfo() {
      try {
        const response = await request({
          url: '/sys/info',
          method: 'get'
        })
        if (response && response.data) {
          this.systemInfo = response.data
        }
      } catch (error) {
        console.log('获取系统信息失败:', error)
      }
    },

    // 文件选择变化处理
    handleFileChange(file) {
      console.log('文件选择:', file.name)

      // 验证文件类型
      const isZip = file.raw.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')
      if (!isZip) {
        this.$message.error('只能上传 ZIP 格式的文件!')
        this.selectedFile = null
        // 清空文件列表
        this.$refs.upload.clearFiles()
        return
      }

      // 验证文件大小
      const isLt100M = file.size / 1024 / 1024 < 100
      if (!isLt100M) {
        this.$message.error('上传文件大小不能超过 100MB!')
        this.selectedFile = null
        // 清空文件列表
        this.$refs.upload.clearFiles()
        return
      }

      // 验证通过，保存文件信息
      this.selectedFile = file.raw
      this.$message.success('文件选择成功，可以开始更新')
    },

    // 重置文件选择
    resetFileSelection() {
      this.selectedFile = null
      this.uploadProgress = 0
      this.uploading = false
      // 清空文件列表
      this.$refs.upload.clearFiles()
      this.$message.info('已清除文件选择，请重新选择更新包')
    },

    // 上传前验证（保留作为备用）
    beforeUpload() {
      // 这个方法在手动上传时会被调用
      return true // 允许上传
    },

    // 手动提交上传
    submitUpload() {
      if (!this.selectedFile) {
        this.$message.warning('请先选择更新包文件')
        return
      }

      // 先拦截
      this.$alert('系统更新无法完成，请联系管理员', '更新失败', {
        confirmButtonText: '确定',
        type: 'error'
      })
    },

    // 上传进度
    handleProgress(event) {
      this.uploadProgress = Math.round(event.percent)
    },

    // 上传成功
    handleSuccess() {
      this.uploading = false
      this.uploadProgress = 100
      this.selectedFile = null

      this.$message.success('系统更新包上传成功！系统正在重启中...')

      // 提示
      this.$alert('系统更新无法完成，请联系管理员', '更新失败', {
        confirmButtonText: '确定',
        type: 'error',
        callback: () => {
          // 延迟刷新页面
          setTimeout(() => {
            window.location.reload()
          }, 2000)
        }
      })
    },

    // 上传失败
    handleError(error) {
      this.uploading = false
      this.uploadProgress = 0
      this.selectedFile = null

      console.error('上传失败:', error)
      this.$message.error('系统更新失败，请检查更新包或联系管理员')
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.update-container {
  max-width: 800px;
  margin: 0 auto;
}

.box-card {
  .card-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
  }
}

.update-content {
  .update-info {
    margin-bottom: 30px;
  }

  .upload-section {
    margin-bottom: 30px;
    text-align: center;

    .upload-demo {
      margin-bottom: 20px;
    }

    .file-info {
      margin-top: 15px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 4px;
      text-align: left;

      p {
        margin: 5px 0;
        color: #606266;
      }
    }

    .progress-section {
      margin-top: 20px;

      .progress-text {
        margin-top: 10px;
        color: #409eff;
        font-size: 14px;
      }
    }
  }

  .current-version {
    margin-top: 30px;
  }
}

::v-deep .el-upload-dragger {
  width: 100%;
  height: 120px;
}

::v-deep .el-descriptions {
  margin-top: 15px;
}
</style>
