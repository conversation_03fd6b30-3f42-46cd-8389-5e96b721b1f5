{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\store\\modules\\user.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\store\\modules\\user.js", "mtime": 1753352678091}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5jb25zdHJ1Y3Rvci5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwppbXBvcnQgeyByZW1vdmVUb2tlbiwgc2V0VG9rZW4gfSBmcm9tICdAL3V0aWxzL2F1dGgnOwppbXBvcnQgcmVxdWVzdCBmcm9tICdAL3V0aWxzL3JlcXVlc3QnOwp2YXIgZ2V0RGVmYXVsdFN0YXRlID0gZnVuY3Rpb24gZ2V0RGVmYXVsdFN0YXRlKCkgewogIHJldHVybiB7CiAgICBhdHRhY2hDb250ZXh0OiAnL2F0dGFjaCcsCiAgICAvLyDpmYTku7bkuIrkuIvmlocKICAgIHRva2VuOiAnJywKICAgIC8vIOeZu+W9leS7pOeJjAogICAgaW5mbzoge30sCiAgICAvLyDnlKjmiLfkv6Hmga8KICAgIG1lbnVzOiBbXSwKICAgIC8vIOiPnOWNlQogICAgZGljdDoge30gLy8g5a2X5YW4CiAgfTsKfTsKdmFyIHN0YXRlID0gZ2V0RGVmYXVsdFN0YXRlKCk7CnZhciBtdXRhdGlvbnMgPSB7CiAgUkVTRVRfU1RBVEU6IGZ1bmN0aW9uIFJFU0VUX1NUQVRFKHN0YXRlKSB7CiAgICBPYmplY3QuYXNzaWduKHN0YXRlLCBnZXREZWZhdWx0U3RhdGUoKSk7CiAgfSwKICBTRVRfVVNFUjogZnVuY3Rpb24gU0VUX1VTRVIoc3RhdGUsIHVzZXIpIHsKICAgIGlmICh1c2VyLmF0dGFjaENvbnRleHQpIHN0YXRlLmF0dGFjaENvbnRleHQgPSB1c2VyLmF0dGFjaENvbnRleHQ7CiAgICBzdGF0ZS50b2tlbiA9IHVzZXIudG9rZW47CiAgICBzdGF0ZS5pbmZvID0gdXNlcjsKICAgIHN0YXRlLm1lbnVzID0gdXNlci5tZW51czsKICAgIHZhciBkaWN0ID0ge307CiAgICBpZiAodXNlci5kaWN0Q29kZXMpIHsKICAgICAgdmFyIGNvZGVzOwogICAgICB1c2VyLmRpY3RDb2Rlcy5mb3JFYWNoKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgY29kZXMgPSBkaWN0W3IudHlwZV07CiAgICAgICAgaWYgKCFjb2RlcykgZGljdFtyLnR5cGVdID0gY29kZXMgPSBbXTsKICAgICAgICBjb2Rlcy5wdXNoKHsKICAgICAgICAgIHZhbHVlOiByLmtleSwKICAgICAgICAgIHRleHQ6IHIudmFsdWUKICAgICAgICB9KTsKICAgICAgfSk7CiAgICB9CiAgICBzdGF0ZS5kaWN0ID0gZGljdDsKICB9Cn07CnZhciBhY3Rpb25zID0gewogIC8vIHVzZXIgbG9naW4KICBsb2dpbjogZnVuY3Rpb24gbG9naW4oX3JlZiwgZGF0YSkgewogICAgdmFyIGNvbW1pdCA9IF9yZWYuY29tbWl0OwogICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHsKICAgICAgcmVxdWVzdCh7CiAgICAgICAgdXJsOiAnL2xvZ2luJywKICAgICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgICBkYXRhOiBkYXRhCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA+IDAgJiYgcmVzLmRhdGEpIHsKICAgICAgICAgIGNvbW1pdCgnU0VUX1VTRVInLCByZXMuZGF0YSk7CiAgICAgICAgICBzZXRUb2tlbihyZXMuZGF0YS50b2tlbik7CiAgICAgICAgICByZXNvbHZlKHJlcyk7CiAgICAgICAgfSBlbHNlIHJlamVjdCgn55m75b2V5aSx6LSlJyk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICByZWplY3QoJ+eZu+W9leW8guW4uCcpOwogICAgICB9KTsKICAgIH0pOwogIH0sCiAgLy8gdXNlciBsb2dvdXQKICBsb2dvdXQ6IGZ1bmN0aW9uIGxvZ291dChfcmVmMikgewogICAgdmFyIGNvbW1pdCA9IF9yZWYyLmNvbW1pdDsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIHJlcXVlc3QoJy9sb2dvdXQnKS50aGVuKGZ1bmN0aW9uIChyKSB7CiAgICAgICAgLy8gcmVzZXRSb3V0ZXIoKQogICAgICAgIGNvbW1pdCgnUkVTRVRfU1RBVEUnKTsKICAgICAgICByZW1vdmVUb2tlbigpOwogICAgICAgIHJlc29sdmUoKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgcmVqZWN0KGVycm9yKTsKICAgICAgfSk7CiAgICB9KTsKICB9LAogIGdldDogZnVuY3Rpb24gZ2V0KF9yZWYzLCB0b2tlbikgewogICAgdmFyIGNvbW1pdCA9IF9yZWYzLmNvbW1pdDsKICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7CiAgICAgIC8vIOmdmem7mOS4jemcgOimgemUmeivr+aPkOekugogICAgICByZXF1ZXN0KHsKICAgICAgICB1cmw6ICcvdXNlcicsCiAgICAgICAgc2lsZW50OiB0cnVlCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKHIpIHsKICAgICAgICBpZiAoci5jb2RlID4gMCAmJiByLmRhdGEpIHsKICAgICAgICAgIGNvbW1pdCgnU0VUX1VTRVInLCByLmRhdGEpOwogICAgICAgICAgc2V0VG9rZW4oci5kYXRhLnRva2VuKTsKICAgICAgICAgIHJlc29sdmUocik7CiAgICAgICAgfSBlbHNlIHJlamVjdCgn5peg5rOV6I635Y+W55So5oi3Jyk7CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIHJlamVjdChlcnJvcik7CiAgICAgIH0pOwogICAgfSk7CiAgfSwKICAvLyByZW1vdmUgdG9rZW4KICByZXNldFRva2VuOiBmdW5jdGlvbiByZXNldFRva2VuKF9yZWY0KSB7CiAgICB2YXIgY29tbWl0ID0gX3JlZjQuY29tbWl0OwogICAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uIChyZXNvbHZlKSB7CiAgICAgIGNvbW1pdCgnUkVTRVRfU1RBVEUnKTsKICAgICAgcmVtb3ZlVG9rZW4oKTsKICAgICAgcmVzb2x2ZSgpOwogICAgfSk7CiAgfQp9OwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZXNwYWNlZDogdHJ1ZSwKICBzdGF0ZTogc3RhdGUsCiAgbXV0YXRpb25zOiBtdXRhdGlvbnMsCiAgYWN0aW9uczogYWN0aW9ucwp9Ow=="}, {"version": 3, "names": ["removeToken", "setToken", "request", "getDefaultState", "attachContext", "token", "info", "menus", "dict", "state", "mutations", "RESET_STATE", "Object", "assign", "SET_USER", "user", "dictCodes", "codes", "for<PERSON>ach", "r", "type", "push", "value", "key", "text", "actions", "login", "_ref", "data", "commit", "Promise", "resolve", "reject", "url", "method", "then", "res", "code", "catch", "logout", "_ref2", "error", "get", "_ref3", "silent", "resetToken", "_ref4", "namespaced"], "sources": ["F:/work/ticai/ticai-web/src/store/modules/user.js"], "sourcesContent": ["import { removeToken, setToken } from '@/utils/auth'\r\nimport request from '@/utils/request'\r\n\r\nconst getDefaultState = () => {\r\n  return {\r\n    attachContext: '/attach', // 附件上下文\r\n    token: '', // 登录令牌\r\n    info: {}, // 用户信息\r\n    menus: [], // 菜单\r\n    dict: {} // 字典\r\n  }\r\n}\r\n\r\nconst state = getDefaultState()\r\n\r\nconst mutations = {\r\n  RESET_STATE: (state) => {\r\n    Object.assign(state, getDefaultState())\r\n  },\r\n  SET_USER: (state, user) => {\r\n    if (user.attachContext) state.attachContext = user.attachContext\r\n    state.token = user.token\r\n    state.info = user\r\n    state.menus = user.menus\r\n    var dict = {}\r\n    if (user.dictCodes) {\r\n      var codes\r\n      user.dictCodes.forEach(r => {\r\n        codes = dict[r.type]\r\n        if (!codes) dict[r.type] = codes = []\r\n        codes.push({ value: r.key, text: r.value })\r\n      })\r\n    }\r\n    state.dict = dict\r\n  }\r\n}\r\n\r\nconst actions = {\r\n  // user login\r\n  login({ commit }, data) {\r\n    return new Promise((resolve, reject) => {\r\n      request({ url: '/login', method: 'post', data: data }).then(res => {\r\n        if (res.code > 0 && res.data) {\r\n          commit('SET_USER', res.data)\r\n          setToken(res.data.token)\r\n          resolve(res)\r\n        } else reject('登录失败')\r\n      }).catch(() => {\r\n        reject('登录异常')\r\n      })\r\n    })\r\n  },\r\n  // user logout\r\n  logout({ commit }) {\r\n    return new Promise((resolve, reject) => {\r\n      request('/logout').then(r => {\r\n        // resetRouter()\r\n        commit('RESET_STATE')\r\n        removeToken()\r\n        resolve()\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n  get({ commit }, token) {\r\n    return new Promise((resolve, reject) => {\r\n      // 静默不需要错误提示\r\n      request({ url: '/user', silent: true }).then(r => {\r\n        if (r.code > 0 && r.data) {\r\n          commit('SET_USER', r.data)\r\n          setToken(r.data.token)\r\n          resolve(r)\r\n        } else reject('无法获取用户')\r\n      }).catch(error => {\r\n        reject(error)\r\n      })\r\n    })\r\n  },\r\n  // remove token\r\n  resetToken({ commit }) {\r\n    return new Promise(resolve => {\r\n      commit('RESET_STATE')\r\n      removeToken()\r\n      resolve()\r\n    })\r\n  }\r\n}\r\n\r\nexport default {\r\n  namespaced: true,\r\n  state,\r\n  mutations,\r\n  actions\r\n}\r\n\r\n"], "mappings": ";;;;AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AACpD,OAAOC,OAAO,MAAM,iBAAiB;AAErC,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC5B,OAAO;IACLC,aAAa,EAAE,SAAS;IAAE;IAC1BC,KAAK,EAAE,EAAE;IAAE;IACXC,IAAI,EAAE,CAAC,CAAC;IAAE;IACVC,KAAK,EAAE,EAAE;IAAE;IACXC,IAAI,EAAE,CAAC,CAAC,CAAC;EACX,CAAC;AACH,CAAC;AAED,IAAMC,KAAK,GAAGN,eAAe,CAAC,CAAC;AAE/B,IAAMO,SAAS,GAAG;EAChBC,WAAW,EAAE,SAAbA,WAAWA,CAAGF,KAAK,EAAK;IACtBG,MAAM,CAACC,MAAM,CAACJ,KAAK,EAAEN,eAAe,CAAC,CAAC,CAAC;EACzC,CAAC;EACDW,QAAQ,EAAE,SAAVA,QAAQA,CAAGL,KAAK,EAAEM,IAAI,EAAK;IACzB,IAAIA,IAAI,CAACX,aAAa,EAAEK,KAAK,CAACL,aAAa,GAAGW,IAAI,CAACX,aAAa;IAChEK,KAAK,CAACJ,KAAK,GAAGU,IAAI,CAACV,KAAK;IACxBI,KAAK,CAACH,IAAI,GAAGS,IAAI;IACjBN,KAAK,CAACF,KAAK,GAAGQ,IAAI,CAACR,KAAK;IACxB,IAAIC,IAAI,GAAG,CAAC,CAAC;IACb,IAAIO,IAAI,CAACC,SAAS,EAAE;MAClB,IAAIC,KAAK;MACTF,IAAI,CAACC,SAAS,CAACE,OAAO,CAAC,UAAAC,CAAC,EAAI;QAC1BF,KAAK,GAAGT,IAAI,CAACW,CAAC,CAACC,IAAI,CAAC;QACpB,IAAI,CAACH,KAAK,EAAET,IAAI,CAACW,CAAC,CAACC,IAAI,CAAC,GAAGH,KAAK,GAAG,EAAE;QACrCA,KAAK,CAACI,IAAI,CAAC;UAAEC,KAAK,EAAEH,CAAC,CAACI,GAAG;UAAEC,IAAI,EAAEL,CAAC,CAACG;QAAM,CAAC,CAAC;MAC7C,CAAC,CAAC;IACJ;IACAb,KAAK,CAACD,IAAI,GAAGA,IAAI;EACnB;AACF,CAAC;AAED,IAAMiB,OAAO,GAAG;EACd;EACAC,KAAK,WAALA,KAAKA,CAAAC,IAAA,EAAaC,IAAI,EAAE;IAAA,IAAhBC,MAAM,GAAAF,IAAA,CAANE,MAAM;IACZ,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC9B,OAAO,CAAC;QAAE+B,GAAG,EAAE,QAAQ;QAAEC,MAAM,EAAE,MAAM;QAAEN,IAAI,EAAEA;MAAK,CAAC,CAAC,CAACO,IAAI,CAAC,UAAAC,GAAG,EAAI;QACjE,IAAIA,GAAG,CAACC,IAAI,GAAG,CAAC,IAAID,GAAG,CAACR,IAAI,EAAE;UAC5BC,MAAM,CAAC,UAAU,EAAEO,GAAG,CAACR,IAAI,CAAC;UAC5B3B,QAAQ,CAACmC,GAAG,CAACR,IAAI,CAACvB,KAAK,CAAC;UACxB0B,OAAO,CAACK,GAAG,CAAC;QACd,CAAC,MAAMJ,MAAM,CAAC,MAAM,CAAC;MACvB,CAAC,CAAC,CAACM,KAAK,CAAC,YAAM;QACbN,MAAM,CAAC,MAAM,CAAC;MAChB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAO,MAAM,WAANA,MAAMA,CAAAC,KAAA,EAAa;IAAA,IAAVX,MAAM,GAAAW,KAAA,CAANX,MAAM;IACb,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC9B,OAAO,CAAC,SAAS,CAAC,CAACiC,IAAI,CAAC,UAAAhB,CAAC,EAAI;QAC3B;QACAU,MAAM,CAAC,aAAa,CAAC;QACrB7B,WAAW,CAAC,CAAC;QACb+B,OAAO,CAAC,CAAC;MACX,CAAC,CAAC,CAACO,KAAK,CAAC,UAAAG,KAAK,EAAI;QAChBT,MAAM,CAACS,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDC,GAAG,WAAHA,GAAGA,CAAAC,KAAA,EAAatC,KAAK,EAAE;IAAA,IAAjBwB,MAAM,GAAAc,KAAA,CAANd,MAAM;IACV,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC;MACA9B,OAAO,CAAC;QAAE+B,GAAG,EAAE,OAAO;QAAEW,MAAM,EAAE;MAAK,CAAC,CAAC,CAACT,IAAI,CAAC,UAAAhB,CAAC,EAAI;QAChD,IAAIA,CAAC,CAACkB,IAAI,GAAG,CAAC,IAAIlB,CAAC,CAACS,IAAI,EAAE;UACxBC,MAAM,CAAC,UAAU,EAAEV,CAAC,CAACS,IAAI,CAAC;UAC1B3B,QAAQ,CAACkB,CAAC,CAACS,IAAI,CAACvB,KAAK,CAAC;UACtB0B,OAAO,CAACZ,CAAC,CAAC;QACZ,CAAC,MAAMa,MAAM,CAAC,QAAQ,CAAC;MACzB,CAAC,CAAC,CAACM,KAAK,CAAC,UAAAG,KAAK,EAAI;QAChBT,MAAM,CAACS,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;EACAI,UAAU,WAAVA,UAAUA,CAAAC,KAAA,EAAa;IAAA,IAAVjB,MAAM,GAAAiB,KAAA,CAANjB,MAAM;IACjB,OAAO,IAAIC,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC5BF,MAAM,CAAC,aAAa,CAAC;MACrB7B,WAAW,CAAC,CAAC;MACb+B,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAe;EACbgB,UAAU,EAAE,IAAI;EAChBtC,KAAK,EAALA,KAAK;EACLC,SAAS,EAATA,SAAS;EACTe,OAAO,EAAPA;AACF,CAAC", "ignoreList": []}]}