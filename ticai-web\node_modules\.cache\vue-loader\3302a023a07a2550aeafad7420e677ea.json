{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backView.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backView.vue", "mtime": 1753352753269}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7IGdldEFzc2V0U3RhdHVzVHlwZSwgZ2V0QXNzZXRTdGF0dXNUZXh0IH0gZnJvbSAnLi4vanMvYXNzZXQuanMnDQoNCmltcG9ydCBBc3NldFZpZXcgZnJvbSAnLi4vYWNjb3VudC9EZXRhaWxWaWV3LnZ1ZScNCmltcG9ydCBVcGxvYWRGaWxlIGZyb20gJ0Avdmlld3MvY29tcG9uZW50cy9VcGxvYWRGaWxlLnZ1ZScNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IEFzc2V0VmlldywgVXBsb2FkRmlsZSB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB2aXNpYmxlOiBmYWxzZSwNCiAgICAgIGZvcm06IHsgfSwNCiAgICAgIHJlZ2lvblRleHQ6ICcnLA0KICAgICAgYXNzZXRMaXN0OiBbXSwNCiAgICAgIGZpbGVMaXN0OiBbXSwNCiAgICAgIGF0dGFjaENvbnRleHQ6IHRoaXMuJHN0b3JlLmdldHRlcnMuYXR0YWNoQ29udGV4dA0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldEFzc2V0U3RhdHVzVHlwZSh2KSB7DQogICAgICByZXR1cm4gZ2V0QXNzZXRTdGF0dXNUeXBlKHYuc3RhdHVzKQ0KICAgIH0sDQogICAgZ2V0QXNzZXRTdGF0dXNUZXh0KHYpIHsNCiAgICAgIHJldHVybiBnZXRBc3NldFN0YXR1c1RleHQodi5zdGF0dXMpDQogICAgfSwNCiAgICBzaG93KGl0ZW0pIHsNCiAgICAgIHRoaXMudmlzaWJsZSA9IHRydWUNCiAgICAgIHRoaXMucmVnaW9uVGV4dCA9IGl0ZW0uZGVwdE5hbWUgJiYgaXRlbS5yZWdpb25OYW1lID8gYCR7aXRlbS5kZXB0TmFtZX0vJHtpdGVtLnJlZ2lvbk5hbWV9YCA6IChpdGVtLmRlcHROYW1lIHx8IGl0ZW0ucmVnaW9uTmFtZSB8fCAnJyk7DQogICAgICB0aGlzLmZvcm0gPSBpdGVtDQogICAgICB0aGlzLmFzc2V0TGlzdCA9IGl0ZW0uZGV0YWlscyB8fCBbXQ0KICAgICAgdGhpcy5maWxlTGlzdCA9IGl0ZW0uZmlsZUxpc3QgfHwgW10NCiAgICB9LA0KICAgIHZpZXdBc3NldChpZCkgew0KICAgICAgdGhpcy4kcmVmcy5hc3NldFZpZXcuc2hvdyhpZCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["backView.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "backView.vue", "sourceRoot": "src/views/asset/back", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog v-dialog-drag title=\"退库申请信息\" width=\"900px\" :visible.sync=\"visible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" size=\"small\" label-width=\"140px\" :model=\"form\">\r\n        <el-form-item label=\"实际退库日期：\">\r\n          <el-input v-model=\"form.time\" readonly class=\"form-static\" style=\"width:100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"使用区域/地点：\">\r\n          <el-input v-model=\"regionText\" readonly class=\"form-static\" style=\"width:100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"领用说明：\">\r\n          <el-input v-model=\"form.memo\" type=\"textarea\" readonly class=\"form-static\" style=\"width:100%\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <upload-file v-if=\"fileList.length\" v-model=\"fileList\" multiple disabled />\r\n      <div style=\"margin-top: 10px;\">退库资产明细：</div>\r\n      <el-divider></el-divider>\r\n      <el-table :data=\"assetList\" size=\"small\" border>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"180\" header-align=\"center\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"130\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"danger\" size=\"mini\" @click.stop=\"viewAsset(scope.row.id)\">{{ scope.row.no }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"180\">\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"primary\" size=\"mini\" @click.stop=\"viewAsset(scope.row.id)\">{{ scope.row.name }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getAssetStatusType(scope.row)\" size=\"small\">{{ getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"visible = false\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <asset-view ref=\"assetView\" />\r\n  </div>\r\n</template>\r\n<script>\r\n\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nimport AssetView from '../account/DetailView.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { AssetView, UploadFile },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      form: { },\r\n      regionText: '',\r\n      assetList: [],\r\n      fileList: [],\r\n      attachContext: this.$store.getters.attachContext\r\n    }\r\n  },\r\n  methods: {\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    show(item) {\r\n      this.visible = true\r\n      this.regionText = item.deptName && item.regionName ? `${item.deptName}/${item.regionName}` : (item.deptName || item.regionName || '');\r\n      this.form = item\r\n      this.assetList = item.details || []\r\n      this.fileList = item.fileList || []\r\n    },\r\n    viewAsset(id) {\r\n      this.$refs.assetView.show(id)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}