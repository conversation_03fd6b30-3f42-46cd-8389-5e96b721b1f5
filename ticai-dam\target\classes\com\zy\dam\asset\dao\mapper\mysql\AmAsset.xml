<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.asset.dao.AmAssetDAO">

    <sql id="meta">
			a.ID_
			,a.PROP_
			,a.TYPE_
			,a.NO_
			,a.NAME_
			,a.SN_
			,a.BC1_
			,a.BC2_
			,a.BC3
			,a.IMP_
			,a.CORP_
			,a.DEPT_
			,a.REGION_
			,a.LOCATION_
			,a.MA_DATE_
			,a.GU_DATE_
			,a.EX_DATE_
			,a.BO_DATE_
			,a.BO_AMOUNT_
			,a.EX_DAY_
			,a.EX_NAME_
			,a.USE_DEPT_
			,a.USE_USER_
			,a.RESERVE1_
			,a.RESERVE2_
			,a.RESERVE3_
			,a.LNG_
			,a.LAT_
			,a.LOC_USER_
			,a.LOC_TIME_
			,a.LOC_ADDR_
			,a.STATUS_
			,a.LEND_STATUS_
			,a.CUSER_
			,a.CTIME_
			,a.MUSER_
			,a.MTIME_
			,a.LAST_ID_
			,a.FLAG_
			,a.PRODUCT_DATE_
			,a.TAKE_DATE_
			,a.SELF_VALUE_
			,a.EXPIRY_MONTH_
			,a.FINANCE_DATE_
			,a.VALUE_
			,a.SPEC_
			,a.BRAND_
			,a.MANU_
			,a.INVOICE_
			,a.SELLER_
			,a.VOUCHER_
			,a.PRICE_
			,a.CONTRACT_
			,a.FROM_ADDR_
			,a.FROM_MEMO_
			,a.MEMO_
			,a.UPLOAD_FLAG_
			,a.PRINT_TIME_
			,a.CUSTODIAN_NAME_
	</sql>

    <sql id="simple_meta">
        a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_
        ,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_,a.SPEC_
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
    </sql>

    <sql id="summary_meta">
        a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_,date_format(a.TAKE_DATE_,'%Y-%m-%d') in_date
        ,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_,a.SPEC_,a.TAKE_DATE_ take_date
        ,b.NAME_ use_user_name,a.LOC_ADDR_,a.LNG_,a.LAT_
        ,c.NAME_ location_name,c.CONTACT_ location_contact, c.PHONE_ location_phone, c.ADDRESS_ location_address
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
    </sql>

    <sql id="location_meta">
        a.ID_,a.PROP_,a.TYPE_,a.NO_,a.NAME_,a.SN_,a.BC1_,a.IMP_,a.CORP_,a.DEPT_,a.REGION_,a.LOCATION_,a.MA_DATE_
        ,a.GU_DATE_,a.EX_DATE_,a.BO_DATE_,a.BO_AMOUNT_,a.EX_DAY_,a.EX_NAME_,a.USE_DEPT_,a.USE_USER_,a.STATUS_,a.LEND_STATUS_,a.SPEC_
        ,b.NAME_ use_user_name
        ,c.NAME_ region_name
        ,d.NAME_ location_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
    </sql>

    <!-- 导出查询的字段列表 -->
    <sql id="export_select">
        a.ID_,a.NO_,a.NAME_,a.PRODUCT_DATE_,a.TAKE_DATE_,a.SELF_VALUE_,a.EXPIRY_MONTH_,a.FINANCE_DATE_,a.VALUE_,a.SPEC_,a.BRAND_,a.MANU_,a.INVOICE_,a.SELLER_,a.VOUCHER_,a.CONTRACT_,a.STATUS_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=b.PCODE_) category_name
        ,b.NAME_ type_name,a.FROM_MEMO_,a.MEMO_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,c.NAME_ location_name,c.CONTACT_ location_contact,c.PHONE_ location_phone,a.LOC_ADDR_
        ,if(a.LNG_ and a.LAT_ IS NOT NULL,'是','否') as whetherLocation
        ,d.NOW_SN_ as nowSn
        ,e.NAME_ as bindUserName
        ,e.ACCOUNT_ as bindUserAccount
        ,e.GENDER_ as bindUserGender
        ,e.STATUS_ as bindUserStatus
        ,(select f.NAME_ from SYS_DEPT f where f.ID_=e.DEPT_) as bindUserDeptName
        ,d.TIME_ as bindTime
    </sql>

    <!-- 导出查询的表连接 -->
    <sql id="export_joins">
        from AM_ASSET a left join AM_ASSET_TYPE b on b.FLAG_='1' and a.TYPE_=b.CODE_
        left join AM_LOCATION c on a.LOCATION_=c.ID_
        left join AM_ASSET_SN d on d.ASSET_=a.ID_ and d.FLAG_!='9'
        left join WX_USER wx on d.USER_=wx.ID_
        left join SYS_USER e on wx.USER_=e.ID_ and e.FLAG_='1'
    </sql>

    <!-- 台账导出查询的字段列表 -->
    <sql id="ledger_export_select">
        a.ID_,a.NO_,a.NAME_,a.PRODUCT_DATE_,a.TAKE_DATE_,a.SELF_VALUE_,a.EXPIRY_MONTH_,a.FINANCE_DATE_,a.VALUE_,a.SPEC_,a.BRAND_,a.MANU_,a.INVOICE_,a.SELLER_,a.VOUCHER_,a.CONTRACT_,a.STATUS_,a.MEMO_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=b.PCODE_) category_name
        ,b.NAME_ type_name
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,c.NAME_ location_name,c.ADDRESS_ location_address,c.CONTACT_ location_contact,c.PHONE_ location_phone,a.LOC_ADDR_
        ,if(a.LNG_ and a.LAT_ IS NOT NULL,'是','否') as whetherLocation
        ,COALESCE(d.NOW_SN_, a.SN_) as nowSn
        ,e.NAME_ as bindUserName
        ,e.ACCOUNT_ as bindUserAccount
        ,e.GENDER_ as bindUserGender
        ,e.STATUS_ as bindUserStatus
        ,(select f.NAME_ from SYS_DEPT f where f.ID_=e.DEPT_) as bindUserDeptName
        ,d.TIME_ as bindTime
    </sql>

    <!-- 台账导出查询的表连接 -->
    <sql id="ledger_export_joins">
        from AM_ASSET a left join AM_ASSET_TYPE b on b.FLAG_='1' and a.TYPE_=b.CODE_
        left join AM_LOCATION c on a.LOCATION_=c.ID_
        left join AM_ASSET_SN d on d.ASSET_=a.ID_ and d.FLAG_!='9'
        left join SYS_USER e on a.USE_USER_=e.ID_ and e.FLAG_='1'
    </sql>

    <!-- 盘点导出查询的字段列表 -->
    <sql id="inventory_export_select">
        b.NAME_ as typeName,
        a.NO_ as no,
        COALESCE(d.NOW_SN_, a.SN_) as nowSn,
        a.NAME_ as name,
        a.SPEC_ as spec,
        (select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) as deptName,
        (select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) as useDeptName,
        (select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) as regionName,
        c.NAME_ as locationName
    </sql>

    <!-- 盘点导出查询的表连接 -->
    <sql id="inventory_export_joins">
        from AM_ASSET a
        left join AM_ASSET_TYPE b on b.FLAG_='1' and a.TYPE_=b.CODE_
        left join AM_LOCATION c on a.LOCATION_=c.ID_
        left join AM_ASSET_SN d on d.ASSET_=a.ID_ and d.FLAG_!='9'
    </sql>

    <!-- 公共查询条件 -->
    <sql id="common_where_conditions">
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like concat('%',#{keyword},'%'))</if>
        <if test="ignoreId != null">and a.ID_!=#{ignoreId}</if>
        <if test="type != null">and a.TYPE_=#{type}</if>
        <if test="typeLike != null">and a.TYPE_ like concat(#{typeLike},'%')</if>
        <if test="types != null and types.size() > 0">
            <foreach collection="types" item="typeItem" open="and a.TYPE_ in (" separator="," close=")">#{typeItem}</foreach>
        </if>
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="location != null">and a.LOCATION_=#{location}</if>
        <if test="dept != null and dept != ''">and a.DEPT_=#{dept}</if>
        <if test="useDept != null and useDept != ''">and a.USE_DEPT_=#{useDept}</if>
        <if test="userName != null">and exists (select 1 from SYS_USER u where u.ID_ = a.USE_USER_ and (u.NAME_ like concat('%', #{userName}, '%') or u.NO_ like concat('%', #{userName}, '%') or u.ACCOUNT_ like concat('%', #{userName}, '%')))</if>
        <if test="imp != null">and a.IMP_=#{imp}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="statusList != null">
            <foreach collection="statusList" item="statusItem" open="and a.STATUS_ in (" separator="," close=")">#{statusItem}</foreach>
        </if>
        <if test="exBegin != null">and a.EX_DATE_ &gt;= #{exBegin}</if>
        <if test="exEnd != null">and a.EX_DATE_ &lt; date_sub(#{exBegin}, interval 1 day)</if>
        <if test="exDateIndex != 0">
            <choose>
                <when test="exDateIndex = 1">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(), interval 91 day)</when>
                <when test="exDateIndex = 2">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(), interval 31 day)</when>
                <when test="exDateIndex = 8">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(), interval 8 day)</when>
                <when test="exDateIndex = 9">and a.EX_DATE_ &lt; curdate()</when>
            </choose>
        </if>
        <if test="(deptIds != null and deptIds.size() > 0) or (useDeptIds != null and useDeptIds.size() > 0)">
            and (
                <choose>
                    <when test="(deptIds != null and deptIds.size() > 0) and (useDeptIds != null and useDeptIds.size() > 0)">
                        (<foreach collection="deptIds" item="deptId" open="a.DEPT_ in (" separator="," close=")">#{deptId}</foreach> or a.DEPT_ is null or a.DEPT_ = '')
                        or
                        (<foreach collection="useDeptIds" item="useDeptId" open="a.USE_DEPT_ in (" separator="," close=")">#{useDeptId}</foreach> or a.USE_DEPT_ is null or a.USE_DEPT_ = '')
                    </when>
                    <when test="deptIds != null and deptIds.size() > 0">
                        (<foreach collection="deptIds" item="deptId" open="a.DEPT_ in (" separator="," close=")">#{deptId}</foreach> or a.DEPT_ is null or a.DEPT_ = '')
                    </when>
                    <when test="useDeptIds != null and useDeptIds.size() > 0">
                        (<foreach collection="useDeptIds" item="useDeptId" open="a.USE_DEPT_ in (" separator="," close=")">#{useDeptId}</foreach> or a.USE_DEPT_ is null or a.USE_DEPT_ = '')
                    </when>
                </choose>
            )
        </if>
        <if test="sn != null">and a.SN_ like concat('%', #{sn}, '%')</if>
        <if test="regionLocation != null">and a.LOCATION_ like concat(#{regionLocation},'%')</if>
        <if test="spec != null">and a.SPEC_ like concat('%',#{spec},'%')</if>
        <if test="name != null">and c.NAME_ like concat('%',#{name},'%')</if>
        <if test="mold != null">and a.TYPE_=#{mold}</if>
        <if test="begin != null">and a.TAKE_DATE_ &gt;= #{begin}</if>
        <if test="end != null">and a.TAKE_DATE_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="locAddr != null">and a.LOC_ADDR_=#{locAddr}</if>
    </sql>

    <resultMap id="VO" type="com.zy.dam.asset.vo.AssetVo">
        <result column="a.ATTR_" jdbcType="BLOB" javaType="String"/>
    </resultMap>

    <!-- 插入数据 -->
    <insert id="insert">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into
        AM_ASSET(ID_,PROP_,TYPE_,NO_,NAME_,SN_,BC1_,BC2_,BC3,IMP_,CORP_,DEPT_,REGION_,LOCATION_,MA_DATE_,GU_DATE_,EX_DATE_,BO_DATE_,BO_AMOUNT_,USE_DEPT_,USE_USER_,RESERVE1_,RESERVE2_,RESERVE3_,ATTR_,STATUS_,CUSER_,CTIME_,MUSER_,MTIME_,FLAG_,
        PRODUCT_DATE_,TAKE_DATE_,SELF_VALUE_,EXPIRY_MONTH_,FINANCE_DATE_,VALUE_,SPEC_,BRAND_,MANU_,INVOICE_,SELLER_,VOUCHER_,PRICE_,CONTRACT_,FROM_ADDR_,FROM_MEMO_,MEMO_,UPLOAD_FLAG_,CUSTODIAN_NAME_)
        values(#{id},#{prop},#{type},#{no},#{name},#{sn},#{bc1},#{bc2},#{bc3},#{imp},#{corp},#{dept},#{region},#{location},#{maDate},#{guDate},#{exDate},#{boDate},#{boAmount},#{useDept},#{useUser},#{reserve1},#{reserve2},#{reserve3},#{attr, jdbcType=BLOB },'1',#{cuser},now(),#{muser},now(),'1',
        #{productDate},#{takeDate},#{selfValue},#{expiryMonth},#{financeDate},#{value},#{spec},#{brand},#{manu},#{invoice},#{seller},#{voucher},#{price},#{contract},#{fromAddr},#{fromMemo},#{memo},#{uploadFlag},#{custodianName})
    </insert>

    <!-- 更新数据 -->
    <update id="update">
		update AM_ASSET
		set PROP_=#{prop},NO_=#{no},NAME_=#{name},SN_=#{sn},BC1_=#{bc1},BC2_=#{bc2},BC3=#{bc3},IMP_=#{imp},CORP_=#{corp},DEPT_=#{dept},REGION_=#{region},LOCATION_=#{location},MA_DATE_=#{maDate},GU_DATE_=#{guDate},EX_DATE_=#{exDate},BO_DATE_=#{boDate},BO_AMOUNT_=#{boAmount},RESERVE1_=#{reserve1},RESERVE2_=#{reserve2},RESERVE3_=#{reserve3},ATTR_=#{attr, jdbcType=BLOB },MUSER_=#{muser},MTIME_=now(),
		PRODUCT_DATE_=#{productDate},TAKE_DATE_=#{takeDate},SELF_VALUE_=#{selfValue},EXPIRY_MONTH_=#{expiryMonth},FINANCE_DATE_=#{financeDate},VALUE_=#{value},SPEC_=#{spec},BRAND_=#{brand},
        MANU_=#{manu},INVOICE_=#{invoice},SELLER_=#{seller},VOUCHER_=#{voucher},PRICE_=#{price},CONTRACT_=#{contract},MEMO_=#{memo}
		where ID_=#{id}
	</update>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="summary_meta"/>
        from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ left join AM_LOCATION c on a.LOCATION_=c.ID_ and c.FLAG_='1'
        where a.FLAG_='1'
        <if test="keyword != null">and (a.NO_ like concat('%',#{keyword},'%') or a.NAME_ like
            concat('%',#{keyword},'%'))
        </if>
        <if test="ignoreId != null">and a.ID_!=#{ignoreId}</if>
        <if test="type != null">and a.TYPE_=#{type}</if>
        <if test="typeLike != null">and a.TYPE_ like concat(#{typeLike},'%')</if>
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="location != null">and a.LOCATION_=#{location}</if>
        <if test="dept != null and dept != ''">and a.DEPT_=#{dept}</if>
        <if test="useDept != null and useDept != ''">and a.USE_DEPT_=#{useDept}</if>
        <if test="sn != null">and a.SN_ like concat('%', #{sn}, '%')</if>
        <if test="userName != null">
            and (b.NAME_ like concat('%', #{userName}, '%') or b.NO_ like concat('%', #{userName},'%') or b.ACCOUNT_
            like concat('%', #{userName}, '%'))
        </if>
        <if test="imp != null">and a.IMP_=#{imp}</if>
        <if test="status != null">and a.STATUS_=#{status}</if>
        <if test="statusList != null">
            <foreach collection="statusList" item="statusItem" open="and a.STATUS_ in (" separator="," close=")">
                #{statusItem}
            </foreach>
        </if>
        <if test="exBegin != null">and a.EX_DATE_ &gt;= #{exBegin}</if>
        <if test="exEnd != null">and a.EX_DATE_ &lt; date_sub(#{exBegin}, interval 1 day)</if>
        <if test="exDateIndex != 0">
            <choose>
                <when test="exDateIndex = 1">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 91 day)
                </when>
                <when test="exDateIndex = 2">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 31 day)
                </when>
                <when test="exDateIndex = 8">and a.EX_DATE_ &gt;= curdate() and a.EX_DATE_ &lt; date_add(curdate(),
                    interval 8 day)
                </when>
                <when test="exDateIndex = 9">and a.EX_DATE_ &lt; curdate()</when>
            </choose>
        </if>
        <if test="(deptIds != null and deptIds.size() > 0) or (useDeptIds != null and useDeptIds.size() > 0)">
            and (
            <choose>
                <when test="(deptIds != null and deptIds.size() > 0) and (useDeptIds != null and useDeptIds.size() > 0)">
                    <!-- 所属部门和使用部门，包含dept为空的数据 -->
                    (<foreach collection="deptIds" item="deptId" open="a.DEPT_ in (" separator="," close=")">#{deptId}</foreach> or a.DEPT_ is null or a.DEPT_ = '')
                    or
                    (<foreach collection="useDeptIds" item="useDeptId" open="a.USE_DEPT_ in (" separator="," close=")">#{useDeptId}</foreach> or a.USE_DEPT_ is null or a.USE_DEPT_ = '')
                </when>
                <when test="deptIds != null and deptIds.size() > 0">
                    <!-- 所属部门，包含dept为空的数据 -->
                    (<foreach collection="deptIds" item="deptId" open="a.DEPT_ in (" separator="," close=")">#{deptId}</foreach> or a.DEPT_ is null or a.DEPT_ = '')
                </when>
                <when test="useDeptIds != null and useDeptIds.size() > 0">
                    <!-- 使用部门，包含useDept为空的数据 -->
                    (<foreach collection="useDeptIds" item="useDeptId" open="a.USE_DEPT_ in (" separator="," close=")">#{useDeptId}</foreach> or a.USE_DEPT_ is null or a.USE_DEPT_ = '')
                </when>
            </choose>
            )
        </if>
        <if test="regionLocation != null">and a.LOCATION_ like concat(#{regionLocation},'%')</if>
        <if test="spec != null">and a.SPEC_ like concat('%',#{spec},'%')</if>
        <if test="name != null">and c.NAME_ like concat('%',#{name},'%')</if>
        <if test="mold != null">and a.TYPE_=#{mold}</if>
        <if test="begin != null">and a.TAKE_DATE_ &gt;= #{begin}</if>
        <if test="end != null">and a.TAKE_DATE_ &lt; date_add(#{end}, interval 1 day)</if>
        <if test="locAddr != null">and a.LOC_ADDR_=#{locAddr}</if>
        group by a.NO_ order by a.CTIME_ desc,a.NO_ desc
    </select>

    <select id="findSummary" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="summary_meta"/>
        from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ left join AM_LOCATION c on a.LOCATION_=c.ID_
        where a.FLAG_='1' and a.ID_=#{0}
    </select>

    <select id="findSummaryByNo" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="summary_meta"/>
        from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ left join AM_LOCATION c on a.LOCATION_=c.ID_
        where a.FLAG_='1' and a.NO_=#{0}
        limit 0,1
    </select>

    <select id="findSummaryByRfid" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="summary_meta"/>
        from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ left join AM_LOCATION c on a.LOCATION_=c.ID_
        where a.FLAG_='1' and a.BC1_=#{0}
        limit 0,1
    </select>

    <select id="findByRegion" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="location_meta"/>
        from AM_ASSET a
        left join SYS_USER b on a.USE_USER_=b.ID_
        left join AM_REGION c on a.REGION_=c.ID_
        left join AM_LOCATION d on a.LOCATION_=d.ID_
        where a.FLAG_='1' and a.status_!='8'
        <if test="regionList != null and regionList.size() != 0">
            <foreach collection="regionList" item="item" separator=" or " open="and (" close=")">
                <choose>
                    <when test="item.region != null">a.REGION_=#{item.region}</when>
                    <otherwise>a.USE_DEPT_=#{item.dept}</otherwise>
                </choose>
            </foreach>
        </if>
        order by c.CODE_,d.CODE_,a.NO_
    </select>

    <!-- 获取唯一的资管-资产基本信息数据 -->
    <select id="findVo" resultMap="VO">
        select
        <include refid="meta"/>
        ,a.ATTR_
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.FLAG_='1' and m.CODE_=a.TYPE_) type_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from SYS_DEPT m where m.FLAG_='1' and m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.FLAG_='1' and m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from SYS_USER m where m.FLAG_='1' and m.ID_=a.LOC_USER_) loc_user_name
        ,b.NAME_ location_name,b.CONTACT_ location_contact, b.PHONE_ location_phone, b.ADDRESS_ location_address
        from AM_ASSET a left join AM_LOCATION b on a.LOCATION_=b.ID_
        where a.ID_=#{0}
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_ASSET set FLAG_='9' where ID_=#{0}
	</update>

    <update id="deleteBatch">
        update AM_ASSET set FLAG_='9' where ID_ in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">#{id}</foreach>
    </update>

    <insert id="insertRel">
		insert into AM_ASSET_REL(ID_,TYPE_,THE_,REL_,FLAG_) values(uuid(), #{0}, #{1}, #{2}, '1')
	</insert>

    <update id="deleteRelNotIn">
        update AM_ASSET_REL set FLAG_='9' where FLAG_='1' and (THE_=#{id} or REL_=#{id})
        <if test="ids != null">
            <foreach collection="ids" item="id0" open="and ID_ in (" separator="," close=")">#{id0}</foreach>
        </if>
    </update>

    <insert id="insertAttach">
		insert into AM_ASSET_ATTACH(ID_,ASSET_,ATTACH_,NAME_,EXT_,ORD_,CTIME_,FLAG_)
		values(uuid(), #{0}, #{1}, #{2}, #{3}, #{4}, now(), '1')
    </insert>

    <update id="deleteAttach">
        update AM_ASSET_ATTACH set FLAG_='9' where FLAG_='1' and ASSET_=#{0}
    </update>

    <select id="findRel" resultType="com.zy.dam.asset.vo.AssetRelVo">
        select a.ID_,a.TYPE_,NULL THE_,a.REL_,
         (select m.NAME_ from AM_ASSET m where m.FLAG_='1' and m.ID_=a.REL_) name_ from AM_ASSET_REL a where a.FLAG_='1' and a.THE_=#{0}
         union all
        select a.ID_,a.TYPE_,a.THE_,NULL REL_,
         (select m.NAME_ from AM_ASSET m where m.FLAG_='1' and m.ID_=a.THE_) name_  from AM_ASSET_REL a where a.FLAG_='1' and a.REL_=#{0}
    </select>

    <select id="findAttach" resultType="com.zy.model.VueFile">
        select a.ATTACH_ id_,a.NAME_,a.EXT_,b.PATH_ from AM_ASSET_ATTACH a,SYS_FILE b
        where a.FLAG_='1' and a.ATTACH_=b.ID_ and a.ASSET_=#{0}
        order by a.ORD_
    </select>

    <insert id="insertOld">
		insert into AM_ASSET(ID_,PROP_,TYPE_,NO_,NAME_,SN_,BC1_,BC2_,BC3,IMP_,CORP_,DEPT_,REGION_,LOCATION_,MA_DATE_,GU_DATE_,EX_DATE_,BO_DATE_,BO_AMOUNT_,EX_DAY_,EX_NAME_,USE_DEPT_,USE_USER_,RESERVE1_,RESERVE2_,RESERVE3_,
		ATTR_,LNG_,LAT_,LOC_USER_,LOC_TIME_,LOC_ADDR_,STATUS_,LEND_STATUS_,CUSER_,CTIME_,MUSER_,MTIME_,LAST_ID_,FLAG_,
		PRODUCT_DATE_,TAKE_DATE_,SELF_VALUE_,EXPIRY_MONTH_,FINANCE_DATE_,VALUE_,SPEC_,BRAND_,MANU_,INVOICE_,SELLER_,VOUCHER_,PRICE_,CONTRACT_,FROM_ADDR_,FROM_MEMO_,MEMO_,UPLOAD_FLAG_)
		select #{1},PROP_,TYPE_,NO_,NAME_,SN_,BC1_,BC2_,BC3,IMP_,CORP_,DEPT_,REGION_,LOCATION_,MA_DATE_,GU_DATE_,EX_DATE_,BO_DATE_,BO_AMOUNT_,EX_DAY_,EX_NAME_,USE_DEPT_,USE_USER_,RESERVE1_,RESERVE2_,RESERVE3_,
		ATTR_,LNG_,LAT_,LOC_USER_,LOC_TIME_,LOC_ADDR_,STATUS_,LEND_STATUS_,CUSER_,CTIME_,MUSER_,MTIME_,LAST_ID_,'9',
		PRODUCT_DATE_,TAKE_DATE_,SELF_VALUE_,EXPIRY_MONTH_,FINANCE_DATE_,VALUE_,SPEC_,BRAND_,MANU_,INVOICE_,SELLER_,VOUCHER_,PRICE_,CONTRACT_,FROM_ADDR_,FROM_MEMO_,MEMO_,UPLOAD_FLAG_
		from AM_ASSET where ID_=#{0}
	</insert>

    <insert id="insertAttachOld">
		insert into AM_ASSET_ATTACH(ID_,ASSET_,ATTACH_,NAME_,EXT_,ORD_,CTIME_,FLAG_)
		select uuid(), #{1}, ATTACH_,NAME_,EXT_,ORD_,CTIME_,'9' from AM_ASSET_ATTACH where ASSET_=#{0}
    </insert>

    <insert id="insertRelOld0">
		insert into AM_ASSET_REL(ID_,TYPE_,THE_,REL_,FLAG_)
		select uuid(),TYPE_,#{1},REL_,'9' from AM_ASSET_REL where THE_=#{0}
	</insert>

    <insert id="insertRelOld1">
		insert into AM_ASSET_REL(ID_,TYPE_,THE_,REL_,FLAG_)
		select uuid(),TYPE_,THE_,#{1},'9' from AM_ASSET_REL where REL_=#{0}
	</insert>

    <select id="findIdByRfid" resultType="String">
        select ID_ from AM_ASSET where FLAG_='1' and BC1_=#{0}
    </select>

    <update id="cleanRfid">
        update AM_ASSET set BC1_=NULL where FLAG_='1' and BC1_=#{0}
    </update>

    <update id="updateRfid">
        update AM_ASSET set BC1_=#{1} where ID_=#{0}
    </update>

    <select id="listUseUser" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="location_meta"/>
        from AM_ASSET a left join SYS_USER b on a.USE_USER_=b.ID_ left join AM_REGION c on a.REGION_=c.ID_ left join
        AM_LOCATION d on a.LOCATION_=d.ID_
        where a.FLAG_='1' and a.USE_USER_=#{0}
        order by c.CODE_,d.CODE_,a.NO_
    </select>

    <select id="findIdByNo" resultType="String">
        select ID_ from AM_ASSET where FLAG_='1' and NO_=#{0} limit 0,1
    </select>

    <select id="findDeptByName" resultType="String">
        select ID_ from SYS_DEPT where FLAG_='1' and NAME_=#{0} limit 0,1
    </select>

    <select id="findUserByName" resultType="String">
        select ID_ from SYS_USER where FLAG_='1' and NAME_=#{0} limit 0,1
    </select>

    <select id="findTypeByName" resultType="String">
        select a.CODE_ from AM_ASSET_TYPE a where a.FLAG_='1' and a.NAME_=#{1} limit 0,1
    </select>

    <select id="findExportByIds" resultType="com.zy.dam.asset.vo.AssetExportVo">
        select
        a.ID_,a.NO_,a.NAME_,a.PRODUCT_DATE_,a.TAKE_DATE_,a.SELF_VALUE_,a.EXPIRY_MONTH_,a.FINANCE_DATE_,a.VALUE_,a.SPEC_,a.BRAND_,a.MANU_,a.INVOICE_,a.SELLER_,a.VOUCHER_,a.CONTRACT_,a.STATUS_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name
        ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=b.PCODE_) category_name
        ,b.NAME_ type_name,a.FROM_MEMO_,a.MEMO_
        ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.USE_DEPT_) use_dept_name
        ,(select m.NAME_ from SYS_USER m where m.ID_=a.USE_USER_) use_user_name
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        ,c.NAME_ location_name,c.CONTACT_ location_contact,c.PHONE_ location_phone,a.LOC_ADDR_
        ,if(a.LNG_ and a.LAT_ IS NOT NULL,'是','否') as whetherLocation
        ,d.NOW_SN_ as nowSn
        ,e.NAME_ as bindUserName
        ,e.ACCOUNT_ as bindUserAccount
        ,e.GENDER_ as bindUserGender
        ,e.STATUS_ as bindUserStatus
        ,(select f.NAME_ from SYS_DEPT f where f.ID_=e.DEPT_) as bindUserDeptName
        ,d.TIME_ as bindTime
        from AM_ASSET a left join AM_ASSET_TYPE b on b.FLAG_='1' and a.TYPE_=b.CODE_
        left join AM_LOCATION c on a.LOCATION_=c.ID_
        left join AM_ASSET_SN d on d.ASSET_=a.ID_ and d.FLAG_!='9'
        left join WX_USER wx on d.USER_=wx.ID_
        left join SYS_USER e on wx.USER_=e.ID_ and e.FLAG_='1'
        where a.FLAG_='1'
        <foreach collection="ids" item="id" open="and a.ID_ in (" separator="," close=")">#{id}</foreach>
        group by a.NO_ order by a.CTIME_ desc,a.NO_ desc
    </select>

    <select id="findExportByQuery" resultType="com.zy.dam.asset.vo.AssetExportVo">
        select <include refid="export_select"/>
        <include refid="export_joins"/>
        where a.FLAG_='1'
        <include refid="common_where_conditions"/>
        group by a.NO_ order by a.CTIME_ desc,a.NO_ desc
    </select>

    <select id="findInventoryExportByQuery" resultType="com.zy.dam.asset.vo.AssetInventoryExportVo">
        select <include refid="inventory_export_select"/>
        <include refid="inventory_export_joins"/>
        where a.FLAG_='1' and a.STATUS_!='8'
        <include refid="common_where_conditions"/>
        group by a.NO_ order by a.CTIME_ desc,a.NO_ desc
    </select>

    <select id="findLedgerExportByIds" resultType="com.zy.dam.asset.vo.AssetLedgerExportVo">
        select <include refid="ledger_export_select"/>
        <include refid="ledger_export_joins"/>
        where a.FLAG_='1'
        <foreach collection="ids" item="id" open="and a.ID_ in (" separator="," close=")">#{id}</foreach>
        group by a.NO_ order by a.CTIME_ desc,a.NO_ desc
    </select>

    <select id="findLedgerExportByQuery" resultType="com.zy.dam.asset.vo.AssetLedgerExportVo">
        select <include refid="ledger_export_select"/>
        <include refid="ledger_export_joins"/>
        where a.FLAG_='1'
        <include refid="common_where_conditions"/>
        group by a.NO_ order by a.CTIME_ desc,a.NO_ desc
    </select>

    <select id="findSpecByType" resultType="String">
        select SPEC_ from (
        select SPEC_,count(0) c from AM_ASSET where TYPE_=#{0} group by SPEC_
        ) a order by a.c desc
    </select>

    <select id="findByLocation" resultType="com.zy.dam.asset.vo.AssetSummaryVo">
        select
        <include refid="simple_meta"/>
        from AM_ASSET a
        where a.FLAG_='1' and a.LOCATION_=#{0}
        order by a.NO_
    </select>

    <select id="findForMap" resultType="com.zy.dam.asset.vo.AssetLatLngVo">
        select ID_,NO_,LAT_,LNG_
        ,case when EX_DATE_ &lt;= b.curr then '3' when EX_DATE_ &lt;= b.near then '2' else '1' end status_
        from AM_ASSET a, (select current_date() curr, date_add(current_date(), interval 30 day) near) b
        where a.FLAG_='1' and a.LNG_ is not null and a.LAT_ is not null
        <if test="deptIds != null">
            and (<foreach collection="deptIds" item="deptId" open="a.DEPT_ in (" separator="," close=")">#{deptId}</foreach> or a.DEPT_ is null or a.DEPT_ = '')
        </if>
        <if test="minX != null and minY != null and maxX != null and maxY != null">
            and a.LNG_ between #{minX}, #{maxX} and a.LAT_ between #{minY}, #{maxY}
        </if>
        order by a.NO_
    </select>

    <update id="checkExpiry">
        update AM_ASSET set EX_DAY_=case when EX_DATE_ is null or DATEDIFF(CURRENT_DATE(), EX_DATE_) &lt; 0 then null
        else DATEDIFF(CURRENT_DATE(), EX_DATE_) end
        where FLAG_!='9'
    </update>

    <!-- 修改资产数据 -->
    <update id="revise">
		update AM_ASSET
		set PROP_=#{prop},NO_=#{no},NAME_=#{name},SN_=#{sn},BC1_=#{bc1},BC2_=#{bc2},BC3=#{bc3},IMP_=#{imp},CORP_=#{corp},DEPT_=#{dept},REGION_=#{region},LOCATION_=#{location},MA_DATE_=#{maDate},GU_DATE_=#{guDate},EX_DATE_=#{exDate},BO_DATE_=#{boDate},BO_AMOUNT_=#{boAmount},RESERVE1_=#{reserve1},RESERVE2_=#{reserve2},RESERVE3_=#{reserve3},ATTR_=#{attr, jdbcType=BLOB },
		PRODUCT_DATE_=#{productDate},TAKE_DATE_=#{takeDate},SELF_VALUE_=#{selfValue},EXPIRY_MONTH_=#{expiryMonth},FINANCE_DATE_=#{financeDate},VALUE_=#{value},SPEC_=#{spec},BRAND_=#{brand},
        MANU_=#{manu},INVOICE_=#{invoice},SELLER_=#{seller},VOUCHER_=#{voucher},PRICE_=#{price},CONTRACT_=#{contract},MEMO_=#{memo},USE_DEPT_=#{useDept},USE_USER_=#{useUser},STATUS_=#{status}
		where ID_=#{id}
	</update>

    <select id="findNoByAsset" resultType="String">
         select NO_ from AM_ASSET where FLAG_='1' and ID_=#{0} limit 0,1
    </select>

    <select id="findByIds" resultType="com.zy.dam.asset.orm.AmAsset">
        select
        <include refid="meta"/>
        from AM_ASSET a
        where a.ID_ in (
        <foreach collection="ids" item="id" separator=",">#{id}</foreach>
        )
    </select>

    <update id="updateLocAddr">
        update AM_ASSET set LOC_ADDR_=#{1} where ID_=#{0}
    </update>

    <!-- 根据资产ID获取网点ID -->
    <select id="findLocationById" resultType="String">
        select LOCATION_ from AM_ASSET where FLAG_='1' and ID_=#{0} limit 0,1
    </select>

    <!-- 根据资产ID获取资产状态 -->
    <select id="findStatusById" resultType="String">
        select STATUS_ from AM_ASSET where FLAG_='1' and ID_=#{0} limit 0,1
    </select>

</mapper>
