{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\permission.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\permission.js", "mtime": 1753352753267}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "store", "NProgress", "getToken", "configure", "showSpinner", "whiteList", "beforeEach", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "to", "from", "next", "user", "moduleName", "token", "wrap", "_callee$", "_context", "prev", "start", "getters", "meta", "name", "title", "path", "document", "query", "id", "dispatch", "indexOf", "done", "then", "r", "code", "concat", "catch", "stop", "_x", "_x2", "_x3", "apply", "arguments", "after<PERSON>ach"], "sources": ["F:/work/ticai/ticai-web/src/permission.js"], "sourcesContent": ["import router from './router'\r\nimport store from './store'\r\nimport NProgress from 'nprogress' // progress bar\r\nimport 'nprogress/nprogress.css' // progress bar style\r\nimport { getToken } from './utils/auth'\r\n\r\nNProgress.configure({ showSpinner: false }) // NProgress Configuration\r\n\r\nconst whiteList = ['/login'] // no redirect whitelist\r\n\r\nrouter.beforeEach(async (to, from, next) => {\r\n  NProgress.start()\r\n  const user = store.getters.user\r\n  var moduleName = (to.meta ? to.meta.name : null) || to.name || ''\r\n  if (to.meta && to.meta.title) {\r\n    if (to.path === '/formDesigner') document.title = to.meta.title + '-' + to.query.name\r\n    else document.title = to.meta.title\r\n  }\r\n  if (user && user.id) {\r\n    store.dispatch('app/setModuleName', moduleName)\r\n    next()\r\n  } else {\r\n    if (whiteList.indexOf(to.path) !== -1) { // 白名单忽略\r\n      next()\r\n    } else {\r\n      NProgress.done()\r\n      const token = getToken()\r\n      if (token) {\r\n        store.dispatch('user/get', token).then(r => {\r\n          if (r.code > 0) {\r\n            store.dispatch('app/setModuleName', moduleName)\r\n            next()\r\n          } else {\r\n            next(`/login?redirect=${to.path}`)\r\n          }\r\n        }).catch(() => {\r\n          next(`/login?redirect=${to.path}`)\r\n        })\r\n      } else next(`/login?redirect=${to.path}`)\r\n    }\r\n  }\r\n})\r\n\r\nrouter.afterEach(() => {\r\n  // finish progress bar\r\n  NProgress.done()\r\n})\r\n"], "mappings": ";;;AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,SAAS,MAAM,WAAW,EAAC;AAClC,OAAO,yBAAyB,EAAC;AACjC,SAASC,QAAQ,QAAQ,cAAc;AAEvCD,SAAS,CAACE,SAAS,CAAC;EAAEC,WAAW,EAAE;AAAM,CAAC,CAAC,EAAC;;AAE5C,IAAMC,SAAS,GAAG,CAAC,QAAQ,CAAC,EAAC;;AAE7BN,MAAM,CAACO,UAAU;EAAA,IAAAC,IAAA,GAAAC,iBAAA,cAAAC,mBAAA,GAAAC,IAAA,CAAC,SAAAC,QAAOC,EAAE,EAAEC,IAAI,EAAEC,IAAI;IAAA,IAAAC,IAAA,EAAAC,UAAA,EAAAC,KAAA;IAAA,OAAAR,mBAAA,GAAAS,IAAA,UAAAC,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAN,IAAA;QAAA;UACrCb,SAAS,CAACqB,KAAK,CAAC,CAAC;UACXP,IAAI,GAAGf,KAAK,CAACuB,OAAO,CAACR,IAAI;UAC3BC,UAAU,GAAG,CAACJ,EAAE,CAACY,IAAI,GAAGZ,EAAE,CAACY,IAAI,CAACC,IAAI,GAAG,IAAI,KAAKb,EAAE,CAACa,IAAI,IAAI,EAAE;UACjE,IAAIb,EAAE,CAACY,IAAI,IAAIZ,EAAE,CAACY,IAAI,CAACE,KAAK,EAAE;YAC5B,IAAId,EAAE,CAACe,IAAI,KAAK,eAAe,EAAEC,QAAQ,CAACF,KAAK,GAAGd,EAAE,CAACY,IAAI,CAACE,KAAK,GAAG,GAAG,GAAGd,EAAE,CAACiB,KAAK,CAACJ,IAAI,MAChFG,QAAQ,CAACF,KAAK,GAAGd,EAAE,CAACY,IAAI,CAACE,KAAK;UACrC;UACA,IAAIX,IAAI,IAAIA,IAAI,CAACe,EAAE,EAAE;YACnB9B,KAAK,CAAC+B,QAAQ,CAAC,mBAAmB,EAAEf,UAAU,CAAC;YAC/CF,IAAI,CAAC,CAAC;UACR,CAAC,MAAM;YACL,IAAIT,SAAS,CAAC2B,OAAO,CAACpB,EAAE,CAACe,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;cAAE;cACvCb,IAAI,CAAC,CAAC;YACR,CAAC,MAAM;cACLb,SAAS,CAACgC,IAAI,CAAC,CAAC;cACVhB,KAAK,GAAGf,QAAQ,CAAC,CAAC;cACxB,IAAIe,KAAK,EAAE;gBACTjB,KAAK,CAAC+B,QAAQ,CAAC,UAAU,EAAEd,KAAK,CAAC,CAACiB,IAAI,CAAC,UAAAC,CAAC,EAAI;kBAC1C,IAAIA,CAAC,CAACC,IAAI,GAAG,CAAC,EAAE;oBACdpC,KAAK,CAAC+B,QAAQ,CAAC,mBAAmB,EAAEf,UAAU,CAAC;oBAC/CF,IAAI,CAAC,CAAC;kBACR,CAAC,MAAM;oBACLA,IAAI,oBAAAuB,MAAA,CAAoBzB,EAAE,CAACe,IAAI,CAAE,CAAC;kBACpC;gBACF,CAAC,CAAC,CAACW,KAAK,CAAC,YAAM;kBACbxB,IAAI,oBAAAuB,MAAA,CAAoBzB,EAAE,CAACe,IAAI,CAAE,CAAC;gBACpC,CAAC,CAAC;cACJ,CAAC,MAAMb,IAAI,oBAAAuB,MAAA,CAAoBzB,EAAE,CAACe,IAAI,CAAE,CAAC;YAC3C;UACF;QAAC;QAAA;UAAA,OAAAP,QAAA,CAAAmB,IAAA;MAAA;IAAA,GAAA5B,OAAA;EAAA,CACF;EAAA,iBAAA6B,EAAA,EAAAC,GAAA,EAAAC,GAAA;IAAA,OAAAnC,IAAA,CAAAoC,KAAA,OAAAC,SAAA;EAAA;AAAA,IAAC;AAEF7C,MAAM,CAAC8C,SAAS,CAAC,YAAM;EACrB;EACA5C,SAAS,CAACgC,IAAI,CAAC,CAAC;AAClB,CAAC,CAAC", "ignoreList": []}]}