{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js??ref--13-0!F:\\work\\ticai\\ticai-web\\src\\store\\getters.js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\store\\getters.js", "mtime": 1753352673374}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\eslint-loader\\index.js", "mtime": 1747730939696}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIGdldHRlcnMgPSB7CiAgc2lkZWJhcjogZnVuY3Rpb24gc2lkZWJhcihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLmFwcC5zaWRlYmFyOwogIH0sCiAgZGV2aWNlOiBmdW5jdGlvbiBkZXZpY2Uoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS5hcHAuZGV2aWNlOwogIH0sCiAgbW9kdWxlTmFtZTogZnVuY3Rpb24gbW9kdWxlTmFtZShzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLmFwcC5tb2R1bGVOYW1lOwogIH0sCiAgYXR0YWNoQ29udGV4dDogZnVuY3Rpb24gYXR0YWNoQ29udGV4dChzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIuYXR0YWNoQ29udGV4dDsKICB9LAogIHRva2VuOiBmdW5jdGlvbiB0b2tlbihzdGF0ZSkgewogICAgcmV0dXJuIHN0YXRlLnVzZXIudG9rZW47CiAgfSwKICB1c2VyOiBmdW5jdGlvbiB1c2VyKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5pbmZvOwogIH0sCiAgbWVudXM6IGZ1bmN0aW9uIG1lbnVzKHN0YXRlKSB7CiAgICByZXR1cm4gc3RhdGUudXNlci5tZW51czsKICB9LAogIGRpY3Q6IGZ1bmN0aW9uIGRpY3Qoc3RhdGUpIHsKICAgIHJldHVybiBzdGF0ZS51c2VyLmRpY3Q7CiAgfSAvLyDlrZflhbjku47nmbvlvZXnlKjmiLfmkLrluKYKfTsKZXhwb3J0IGRlZmF1bHQgZ2V0dGVyczs="}, {"version": 3, "names": ["getters", "sidebar", "state", "app", "device", "moduleName", "attachContext", "user", "token", "info", "menus", "dict"], "sources": ["F:/work/ticai/ticai-web/src/store/getters.js"], "sourcesContent": ["const getters = {\r\n  sidebar: state => state.app.sidebar,\r\n  device: state => state.app.device,\r\n  moduleName: state => state.app.moduleName,\r\n  attachContext: state => state.user.attachContext,\r\n  token: state => state.user.token,\r\n  user: state => state.user.info,\r\n  menus: state => state.user.menus,\r\n  dict: state => state.user.dict // 字典从登录用户携带\r\n}\r\nexport default getters\r\n"], "mappings": "AAAA,IAAMA,OAAO,GAAG;EACdC,OAAO,EAAE,SAATA,OAAOA,CAAEC,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACF,OAAO;EAAA;EACnCG,MAAM,EAAE,SAARA,MAAMA,CAAEF,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACC,MAAM;EAAA;EACjCC,UAAU,EAAE,SAAZA,UAAUA,CAAEH,KAAK;IAAA,OAAIA,KAAK,CAACC,GAAG,CAACE,UAAU;EAAA;EACzCC,aAAa,EAAE,SAAfA,aAAaA,CAAEJ,KAAK;IAAA,OAAIA,KAAK,CAACK,IAAI,CAACD,aAAa;EAAA;EAChDE,KAAK,EAAE,SAAPA,KAAKA,CAAEN,KAAK;IAAA,OAAIA,KAAK,CAACK,IAAI,CAACC,KAAK;EAAA;EAChCD,IAAI,EAAE,SAANA,IAAIA,CAAEL,KAAK;IAAA,OAAIA,KAAK,CAACK,IAAI,CAACE,IAAI;EAAA;EAC9BC,KAAK,EAAE,SAAPA,KAAKA,CAAER,KAAK;IAAA,OAAIA,KAAK,CAACK,IAAI,CAACG,KAAK;EAAA;EAChCC,IAAI,EAAE,SAANA,IAAIA,CAAET,KAAK;IAAA,OAAIA,KAAK,CAACK,IAAI,CAACI,IAAI;EAAA,EAAC;AACjC,CAAC;AACD,eAAeX,OAAO", "ignoreList": []}]}