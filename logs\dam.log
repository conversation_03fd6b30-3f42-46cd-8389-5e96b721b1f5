07-25 10:43:27.649 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 10:43:27.651 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 34096 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-25 10:43:27.653 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-25 10:43:28.487 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 10:43:28.490 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 10:43:28.522 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
07-25 10:43:28.732 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-25 10:43:29.018 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-25 10:43:29.025 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-25 10:43:29.026 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-25 10:43:29.026 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-25 10:43:29.202 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-25 10:43:29.202 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1516 ms
07-25 10:43:29.232 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-25 10:43:29.299 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 10:43:29.814 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 10:43:31.448 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 10:43:31.456 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 10:43:31.456 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 10:43:31.456 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 10:43:31.456 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 10:43:31.457 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 10:43:31.457 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 10:43:31.457 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@43c20142
07-25 10:43:31.645 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 10:43:31.704 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 10:43:31.755 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-25 10:43:31.778 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-25 10:43:31.943 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 10:43:31.944 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 10:43:31.953 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 6.052 seconds (JVM running for 7.65)
07-25 10:43:57.438 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 10:43:57.600 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 10:43:57.600 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 10:43:57.600 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 10:43:57.600 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 10:43:57.627 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 10:43:57.891 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-25 10:44:01.268 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 10:44:01.270 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 34468 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-25 10:44:01.272 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-25 10:44:01.861 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 10:44:01.862 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 10:44:01.881 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
07-25 10:44:02.030 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-25 10:44:02.263 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-25 10:44:02.269 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-25 10:44:02.269 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-25 10:44:02.269 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-25 10:44:02.369 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-25 10:44:02.369 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1073 ms
07-25 10:44:02.398 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-25 10:44:02.441 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 10:44:02.916 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 10:44:04.287 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 10:44:04.292 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 10:44:04.292 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 10:44:04.293 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 10:44:04.293 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 10:44:04.293 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 10:44:04.293 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 10:44:04.293 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2e6f1f48
07-25 10:44:04.475 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 10:44:04.515 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 10:44:04.566 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-25 10:44:04.585 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-25 10:44:04.746 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 10:44:04.746 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 10:44:04.755 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 4.956 seconds (JVM running for 5.651)
07-25 10:45:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:45:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:45:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:45:00.158 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:46:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:46:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:46:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:46:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:46:13.113 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 10:46:13.252 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 10:46:13.252 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 10:46:13.252 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 10:46:13.253 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 10:46:13.271 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 10:46:13.521 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
07-25 10:46:16.697 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
07-25 10:46:16.700 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 35624 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
07-25 10:46:16.701 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
07-25 10:46:17.233 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
07-25 10:46:17.234 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
07-25 10:46:17.251 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
07-25 10:46:17.397 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=1b44a730-e903-3697-99d9-e7814c6b20b7
07-25 10:46:17.614 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
07-25 10:46:17.619 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
07-25 10:46:17.619 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
07-25 10:46:17.620 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
07-25 10:46:17.708 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
07-25 10:46:17.708 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 981 ms
07-25 10:46:17.732 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
07-25 10:46:17.777 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
07-25 10:46:18.254 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
07-25 10:46:19.554 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
07-25 10:46:19.559 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
07-25 10:46:19.559 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
07-25 10:46:19.560 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
07-25 10:46:19.560 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

07-25 10:46:19.560 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
07-25 10:46:19.560 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
07-25 10:46:19.560 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2e6f1f48
07-25 10:46:19.733 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
07-25 10:46:19.768 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
07-25 10:46:19.818 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
07-25 10:46:19.837 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
07-25 10:46:20.001 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
07-25 10:46:20.001 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
07-25 10:46:20.010 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 4.748 seconds (JVM running for 5.395)
07-25 10:47:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:47:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:47:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:47:00.190 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:47:39.433 [http-nio-20000-exec-2] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
07-25 10:47:39.433 [http-nio-20000-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
07-25 10:47:39.434 [http-nio-20000-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
07-25 10:47:39.451 [http-nio-20000-exec-2] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/
07-25 10:47:49.345 [http-nio-20000-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/
07-25 10:48:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:48:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:48:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:48:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:49:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:49:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:49:00.155 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:49:00.182 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:49:00.750 [http-nio-20000-exec-9] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [115] milliseconds.
07-25 10:50:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:50:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:50:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:50:00.173 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:51:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:51:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:51:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:51:00.178 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:52:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:52:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:52:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:52:00.195 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:53:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:53:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:53:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:53:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:54:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:54:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:54:00.155 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:54:00.184 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:55:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:55:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:55:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:55:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:56:00.063 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:56:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:56:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:56:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202506月份的报表已经存在
07-25 10:56:54.995 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 10:56:55.159 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
07-25 10:56:55.159 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
07-25 10:56:55.159 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
07-25 10:56:55.160 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
07-25 10:56:55.325 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
07-25 10:56:55.673 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
