{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js!F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\bd\\region\\index.vue", "mtime": 1753352753270}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}, {"path": "F:\\work\\ticai\\ticai-web\\babel.config.js", "mtime": 1747726162747}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["Loading", "PageTable", "Region", "TreeBox", "UserChosen", "MapLocation", "UploadFile", "components", "data", "regionList", "deptTree", "activeItem", "tableHeight", "regionVisible", "regionData", "region", "regionRules", "code", "required", "message", "trigger", "name", "dept", "qform", "keyword", "locationVisible", "locationData", "locationRules", "fileList", "uploadList", "uploadVisible", "showUploadAll", "amLocationAsset", "batchFileList", "batchUploadList", "batchUploadVisible", "mounted", "loadDeptTree", "loadRegion", "searchLocation", "$refs", "grid", "setMaxHeight", "Math", "max", "document", "documentElement", "clientHeight", "methods", "formatDateTime", "dateTime", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "concat", "_this", "$http", "then", "res", "catch", "$alert", "_this2", "id", "i", "length", "showRegion", "item", "addRegion", "ord", "editRegion", "json", "JSON", "stringify", "parse", "saveRegion", "_this3", "regionform", "validate", "valid", "url", "$message", "success", "removeRegion", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "search", "addLocation", "editLocation", "_this5", "saveLocation", "_this6", "locationform", "details", "for<PERSON>ach", "r", "push", "sn", "warning", "removeLocation", "_this7", "mapPin", "ll", "lat", "lng", "mapLocation", "show", "pined", "$set", "address", "lnglat", "upload", "uploadRemove", "toggleErr", "uploaded", "_this8", "loadInst", "service", "fullscreen", "text", "close", "submitUpload", "_this9", "$emit", "error", "addAsset", "removeAsset", "rowIndex", "splice", "handleBatchCommand", "command", "exportBatch", "importBatch", "_this0", "$jasper", "responseType", "blob", "$saveAs", "err", "batchUploadRemove", "removeBatchRow", "index", "_this1", "getBatchErrorCount", "filter", "rowMsg", "batchUploaded", "_this10", "submitBatchUpdate", "_this11", "validData", "errorCount", "checkDuplicatesAndUpdate", "checkAndConfirmDuplicates", "_this12", "duplicateInfo", "hasDuplicates", "importDuplicates", "dbDuplicates", "join", "dangerouslyUseHTMLString", "doBatchUpdate", "_this13"], "sources": ["src/views/bd/region/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"layout-lr\">\r\n    <div class=\"left\">\r\n      <div class=\"head\">区域列表</div>\r\n      <div class=\"body\">\r\n        <ul>\r\n          <li :class=\"{ act: activeItem == null || activeItem.id == null }\" @click=\"showRegion({})\">所有区域</li>\r\n          <li v-for=\"item in regionList\" :key=\"item.id\" :class=\"{ act: activeItem && activeItem.id == item.id }\"\r\n            @click=\"showRegion(item)\">{{ item.name }}</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n    <div class=\"center\">\r\n      <el-descriptions title=\"区域信息\" :column=\"3\" border class=\"descr-3\">\r\n        <template slot=\"extra\">\r\n          <div class=\"button-bar\">\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addRegion\">新增区域</el-button>\r\n            <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-upload\" @click=\"upload\">网点导入</el-button>\r\n            <template v-if=\"activeItem && activeItem.id\">\r\n              <el-button type=\"primary\" size=\"mini\" icon=\"el-icon-edit\" @click=\"editRegion\">编辑区域</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click=\"removeRegion\">删除区域</el-button>\r\n              <el-button type=\"success\" size=\"mini\" icon=\"el-icon-plus\" @click=\"addLocation\">新增网点</el-button>\r\n              <el-dropdown @command=\"handleBatchCommand\" style=\"margin-left:10px\">\r\n                <el-button type=\"warning\" size=\"mini\">\r\n                  批量修改<i class=\"el-icon-arrow-down el-icon--right\"></i>\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"export\">导出</el-dropdown-item>\r\n                  <el-dropdown-item command=\"import\">导入</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </div>\r\n        </template>\r\n        <template v-if=\"activeItem && activeItem.id\">\r\n          <el-descriptions-item label=\"所属部门\">{{ activeItem.deptName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域代码\">{{ activeItem.code }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"区域名称\">{{ activeItem.name }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"所在区划\">{{ activeItem.regionName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"负责人\">{{ activeItem.userName }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"联系电话\">{{ activeItem.userPhone }}</el-descriptions-item>\r\n          <el-descriptions-item :span=\"3\" label=\"区域范围\">{{ activeItem.scope }}</el-descriptions-item>\r\n        </template>\r\n      </el-descriptions>\r\n      <el-divider></el-divider>\r\n      <div class=\"location-head\">\r\n        <span class=\"location-title\">区域网点</span>\r\n        <div class=\"location-search\">\r\n          <el-input v-model=\"qform.keyword\" clearable size=\"mini\" placeholder=\"输入关键字\" autocomplete=\"off\">\r\n            <template slot=\"prepend\">检索:</template>\r\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"searchLocation\"></el-button>\r\n          </el-input>\r\n        </div>\r\n      </div>\r\n      <page-table ref=\"grid\" size=\"mini\" path=\"/am/location/page\" :query=\"qform\" stripe border>\r\n        <el-table-column v-if=\"activeItem == null || activeItem.id == null\" label=\"所属区域\" prop=\"regionName\" width=\"100\"\r\n          align=\"center\" />\r\n        <el-table-column label=\"网点编码\" prop=\"code\" width=\"110\" align=\"center\" />\r\n        <el-table-column label=\"网点名称\" prop=\"name\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"负责人\" prop=\"contact\" width=\"300\" align=\"center\" />\r\n        <el-table-column label=\"联系方式\" prop=\"phone\" width=\"150\" align=\"center\" />\r\n        <el-table-column label=\"地址\" prop=\"address\" width=\"800\" align=\"center\" />\r\n        <el-table-column label=\"网点时间\" prop=\"createTime\" align=\"center\" width=\"150\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.createTime ? formatDateTime(scope.row.createTime) : '' }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"120\" align=\"center\" fixed=\"right\">\r\n          <template slot-scope=\"{ row }\">\r\n            <div v-if=\"row.id != 'admin'\">\r\n              <el-button type=\"primary\" size=\"mini\" @click.stop=\"editLocation(row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"mini\" @click.stop=\"removeLocation(row)\">删除</el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </page-table>\r\n    </div>\r\n    <el-dialog v-dialog-drag title=\"区域信息\" width=\"800px\" :visible.sync=\"regionVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"regionform\" :model=\"regionData\" :rules=\"regionRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\" prop=\"code\">\r\n              <el-input v-model=\"regionData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\" prop=\"name\">\r\n              <el-input v-model=\"regionData.name\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所属机构：\" prop=\"dept\">\r\n              <tree-box v-model=\"regionData.dept\" :data=\"deptTree\" :expand-all=\"true\" :clearable=\"false\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"所在区划：\" prop=\"region\">\r\n              <region v-model=\"regionData.region\" root=\"460000\" :start-level=\"1\" with-root any-node\r\n                style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"user\">\r\n              <user-chosen v-model=\"regionData.user\" type=\"1\" style=\"width: 100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域排序：\" prop=\"ord\">\r\n              <el-input-number v-model=\"regionData.ord\" autocomplete=\"off\" :min=\"1\" :max=\"999\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"区域范围：\" prop=\"scope\">\r\n              <el-input v-model=\"regionData.scope\" maxlength=\"32\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"regionVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveRegion\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog v-dialog-drag title=\"区域地点\" width=\"800px\" :visible.sync=\"locationVisible\" :close-on-press-escape=\"false\"\r\n      :close-on-click-modal=\"false\">\r\n      <el-form ref=\"locationform\" :model=\"locationData\" :rules=\"locationRules\" label-width=\"110px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域编码：\">\r\n              <el-input v-model=\"activeItem.code\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"区域名称：\">\r\n              <el-input v-model=\"activeItem.name\" readonly class=\"form-static\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点编码：\" prop=\"code\">\r\n              <el-input v-model=\"locationData.code\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"网点名称：\" prop=\"name\">\r\n              <el-input v-model=\"locationData.name\" maxlength=\"64\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"负责人：\" prop=\"contact\">\r\n              <el-input v-model=\"locationData.contact\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话：\" prop=\"phone\">\r\n              <el-input v-model=\"locationData.phone\" maxlength=\"20\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点地址：\" prop=\"address\">\r\n              <el-input v-model=\"locationData.address\" maxlength=\"128\" autocomplete=\"off\">\r\n                <template slot=\"append\">\r\n                  <el-button size=\"mini\" icon=\"el-icon-map-location\" @click=\"mapPin\">地图定位</el-button>\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"网点备注：\" prop=\"memo\">\r\n              <el-input v-model=\"locationData.memo\" maxlength=\"128\" autocomplete=\"off\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table ref=\"optionGrid\" :data=\"amLocationAsset\" size=\"mini\" :stripe=\"true\" :border=\"true\">\r\n        <el-table-column type=\"index\" width=\"50\" align=\"center\" />\r\n        <el-table-column label=\"销售终端编号\" prop=\"sn\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.sn\" size=\"mini\" autocomplete=\"off\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column width=\"70\" align=\"center\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-button size=\"mini\" type=\"danger\" @click.stop=\"removeAsset(scope.$index)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"locationVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveLocation\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <!--------网点导入------->\r\n    <el-dialog ref=\"uploadDlg\" title=\"批量导入\" fullscreen class=\"dialog-full\" :visible.sync=\"uploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"fileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"uploaded\" @removeFile=\"uploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th>结果提示</th>\r\n              <th>所属市县编号</th>\r\n              <th>销售终端编号</th>\r\n              <th>门店编号</th>\r\n              <th>业主姓名</th>\r\n              <th>负责人</th>\r\n              <th>联系方式</th>\r\n              <th>门店地址</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"item in uploadList\">\r\n              <tr v-if=\"showUploadAll || item.rowMsg != null\" :key=\"item.rowNum\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ item.rowNum }}</td>\r\n                <td class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.region }}</td>\r\n                <td>{{ item.sn }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>{{ item.contact }}</td>\r\n                <td>{{ item.phone }}</td>\r\n                <td>{{ item.address }}</td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <el-button size=\"small\" type=\"warning\" @click=\"toggleErr\">{{ showUploadAll ? '查看错误' : '查看全部' }}</el-button>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"uploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitUpload\">确定上传</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <el-dialog ref=\"batchUploadDlg\" title=\"网点批量修改\" fullscreen class=\"dialog-full\" :visible.sync=\"batchUploadVisible\"\r\n      :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <div style=\"margin: 10px 20px;\">\r\n        <upload-file v-model=\"batchFileList\" type=\"ASSET_UPLOAD\" simple :multiple=\"false\" :limit=\"1\" accept=\".xls,.xlsx\"\r\n          @success=\"batchUploaded\" @removeFile=\"batchUploadRemove\" />\r\n      </div>\r\n      <div class=\"upload-block\">\r\n        <table>\r\n          <thead>\r\n            <tr>\r\n              <th>行号</th>\r\n              <th v-if=\"getBatchErrorCount() > 0\">结果提示</th>\r\n              <th>网点编码</th>\r\n              <th>网点名称</th>\r\n              <th>网点备注</th>\r\n              <th>操作</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <template v-for=\"(item, index) in batchUploadList\">\r\n              <tr :key=\"index\" :class=\"{ err: item.rowMsg != null }\">\r\n                <td>{{ index + 1 }}</td>\r\n                <td v-if=\"getBatchErrorCount() > 0\" class=\"upload-msg\">{{ item.rowMsg }}</td>\r\n                <td>{{ item.code }}</td>\r\n                <td>{{ item.name }}</td>\r\n                <td>\r\n                  <el-input\r\n                    v-model=\"item.memo\"\r\n                    size=\"mini\"\r\n                    placeholder=\"请输入网点备注\"\r\n                    maxlength=\"200\"\r\n                    show-word-limit\r\n                    clearable\r\n                    style=\"width: 100%;\"\r\n                  />\r\n                </td>\r\n                <td>\r\n                  <el-button type=\"danger\" size=\"mini\" @click=\"removeBatchRow(index)\">删除</el-button>\r\n                </td>\r\n              </tr>\r\n            </template>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <div style=\"left: 30px; float: left;\">\r\n          <span style=\"color: #909399; font-size: 12px;\">\r\n            共 {{ batchUploadList.length }} 条数据，其中 {{ getBatchErrorCount() }} 条错误\r\n          </span>\r\n        </div>\r\n        <el-button size=\"small\" @click=\"batchUploadVisible = false\">取 消</el-button>\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitBatchUpdate\">确定更新</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <map-location ref=\"mapLocation\" @success=\"pined\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport { Loading } from 'element-ui'\r\nimport PageTable from '@/views/components/PageTable.vue'\r\nimport Region from '@/views/components/Region.vue'\r\nimport TreeBox from '@/views/components/TreeBox.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport MapLocation from '@/views/map/util/location.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { PageTable, Region, TreeBox, UserChosen, MapLocation, UploadFile },\r\n  data() {\r\n    return {\r\n      regionList: [],\r\n      deptTree: [], // 机构数据列表\r\n      activeItem: {},\r\n      tableHeight: 300,\r\n      regionVisible: false,\r\n      regionData: { region: '' },\r\n      regionRules: {\r\n        code: [{ required: true, message: '请输入区域编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入区域名称', trigger: 'blur' }],\r\n        dept: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],\r\n        region: [{ required: true, message: '请选择所在区划', trigger: 'blur' }]\r\n      },\r\n      qform: { keyword: '' },\r\n      locationVisible: false,\r\n      locationData: { region: '' },\r\n      locationRules: {\r\n        code: [{ required: true, message: '请输入地点编码', trigger: 'blur' }],\r\n        name: [{ required: true, message: '请输入地点名称', trigger: 'blur' }]\r\n      },\r\n      fileList: [],\r\n      uploadList: [],\r\n      uploadVisible: false,\r\n      showUploadAll: true,\r\n      amLocationAsset: [{}],\r\n      // 批量修改相关\r\n      batchFileList: [],\r\n      batchUploadList: [],\r\n      batchUploadVisible: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.loadDeptTree()\r\n    this.loadRegion()\r\n    this.searchLocation()\r\n    this.$refs.grid.setMaxHeight(Math.max(document.documentElement.clientHeight - 380, 200))\r\n  },\r\n  methods: {\r\n    formatDateTime(dateTime) {\r\n      if (!dateTime) return ''\r\n      const date = new Date(dateTime)\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, '0')\r\n      const day = String(date.getDate()).padStart(2, '0')\r\n      const hours = String(date.getHours()).padStart(2, '0')\r\n      const minutes = String(date.getMinutes()).padStart(2, '0')\r\n      const seconds = String(date.getSeconds()).padStart(2, '0')\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`\r\n    },\r\n    loadDeptTree() {\r\n      this.$http('/sys/dept/treeByType/1').then(res => {\r\n        this.deptTree = res\r\n      }).catch(() => { this.$alert('加载机构树出错') })\r\n    },\r\n    loadRegion() {\r\n      this.$http('/am/region/list').then(res => {\r\n        this.regionList = res || []\r\n        if (this.activeItem && this.activeItem.id) {\r\n          for (let i = 0; i < res.length; i++) {\r\n            if (res[i].id === this.activeItem.id) {\r\n              this.activeItem = res[i]\r\n              break\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n\r\n    showRegion(item) {\r\n      this.activeItem = item\r\n      this.qform.region = item.id\r\n      this.qform.keyword = ''\r\n      this.searchLocation()\r\n    },\r\n    addRegion() {\r\n      this.regionVisible = true\r\n      this.regionData = { ord: 1 }\r\n    },\r\n    editRegion() {\r\n      if (!this.activeItem.id) return\r\n      this.regionVisible = true\r\n      const json = JSON.stringify(this.activeItem)\r\n      this.regionData = JSON.parse(json)\r\n    },\r\n    saveRegion() {\r\n      this.$refs.regionform.validate(valid => {\r\n        if (valid) {\r\n          this.$http({ url: '/am/region/save', data: this.regionData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.regionVisible = false\r\n              this.loadRegion()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeRegion() {\r\n      this.$confirm('此操作将永久删除该区域, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/region/delete/' + this.activeItem.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.activeItem = {}\r\n            this.loadRegion()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    searchLocation() {\r\n      this.$refs.grid.search(this.qform)\r\n    },\r\n    addLocation() {\r\n      this.locationVisible = true\r\n      this.locationData = { region: this.activeItem.id }\r\n      this.amLocationAsset = []\r\n    },\r\n    // editLocation(item) {\r\n    //   this.locationVisible = true\r\n    //   const json = JSON.stringify(item)\r\n    //   this.locationData = JSON.parse(json)\r\n    // },\r\n    editLocation(item) {\r\n      this.$http('/am/location/get/' + item.id).then(res => {\r\n        if (res.code > 0 && res.data) {\r\n          this.locationVisible = true\r\n          this.locationData = res.data\r\n          this.amLocationAsset = res.data.amLocationAsset || []\r\n        }\r\n      })\r\n    },\r\n    saveLocation() {\r\n      this.$refs.locationform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.amLocationAsset.forEach(r => details.push({ sn: r.sn }))\r\n          if (!details.length) return this.$message.warning('请录入终端信息')\r\n          this.locationData.amLocationAsset = details\r\n          this.$http({ url: '/am/location/saveDevice', data: this.locationData }).then(res => {\r\n            if (res.code > 0) {\r\n              this.$message.success('保存区域信息成功')\r\n              this.locationVisible = false\r\n              this.searchLocation()\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    removeLocation(item) {\r\n      this.$confirm('此操作将永久删除该地点, 是否继续?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.$http({ url: '/am/location/delete/' + item.id }).then(res => {\r\n          if (res.code > 0) {\r\n            this.$message.success('删除成功')\r\n            this.searchLocation()\r\n          }\r\n        })\r\n      }).catch(() => { })\r\n    },\r\n    mapPin() {\r\n      const ll = this.locationData.lat ? { lng: this.locationData.lng, lat: this.locationData.lat } : null\r\n      this.$refs.mapLocation.show(ll)\r\n    },\r\n    pined(r) {\r\n      this.$set(this.locationData, 'address', r.address)\r\n      this.$set(this.locationData, 'lng', r.lnglat ? r.lnglat.lng : null)\r\n      this.$set(this.locationData, 'lat', r.lnglat ? r.lnglat.lat : null)\r\n    },\r\n    upload() {\r\n      this.fileList = []\r\n      this.uploadList = []\r\n      this.uploadVisible = true\r\n    },\r\n    uploadRemove() {\r\n      this.uploadList = []\r\n    },\r\n    toggleErr() {\r\n      this.showUploadAll = !this.showUploadAll\r\n    },\r\n    uploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.showUploadAll = true\r\n            this.uploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitUpload() {\r\n      if (this.uploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/uploadData', data: this.uploadList }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success('上传成功')\r\n          this.$emit('success')\r\n          this.uploadVisible = false\r\n          this.search()\r\n        } else if (res.code === 2) {\r\n          this.uploadList = res.data\r\n          this.$message.error('存在错误的数据行')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n        // this.$message.error('网络超时')\r\n      })\r\n    },\r\n    addAsset() {\r\n      this.amLocationAsset.push({})\r\n    },\r\n    removeAsset(rowIndex) {\r\n      this.amLocationAsset.splice(rowIndex, 1)\r\n    },\r\n    // 批量修改相关方法\r\n    handleBatchCommand(command) {\r\n      if (command === 'export') {\r\n        this.exportBatch()\r\n      } else if (command === 'import') {\r\n        this.importBatch()\r\n      }\r\n    },\r\n    exportBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      const loadInst = Loading.service({ fullscreen: true, text: '正在导出文件，请耐心等待...' })\r\n      this.$jasper({ url: `/am/location/exportBatch/${this.activeItem.id}`, responseType: 'blob' }).then(blob => {\r\n        loadInst.close()\r\n        this.$saveAs(blob, '网点批量修改模板.xlsx')\r\n      }).catch(err => {\r\n        loadInst.close()\r\n        this.$message.error('导出生成出错:' + err)\r\n      })\r\n    },\r\n    importBatch() {\r\n      if (!this.activeItem || !this.activeItem.id) {\r\n        return this.$message.warning('请先选择一个区域')\r\n      }\r\n      this.batchFileList = []\r\n      this.batchUploadList = []\r\n      this.batchUploadVisible = true\r\n    },\r\n    batchUploadRemove() {\r\n      this.batchUploadList = []\r\n    },\r\n    removeBatchRow(index) {\r\n      this.$confirm('确定要删除这条数据吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.batchUploadList.splice(index, 1)\r\n        this.$message.success('删除成功')\r\n      }).catch(() => {})\r\n    },\r\n    getBatchErrorCount() {\r\n      return this.batchUploadList.filter(item => item.rowMsg != null).length\r\n    },\r\n    batchUploaded(fileList) {\r\n      if (fileList && fileList.length) {\r\n        const loadInst = Loading.service({ fullscreen: true, text: '解析文件中...' })\r\n        this.$http({ url: '/am/location/uploadBatchFile', data: fileList[0] }).then(res => {\r\n          if (res.code > 0) {\r\n            this.batchUploadList = res.data\r\n          }\r\n          loadInst.close()\r\n        })\r\n      }\r\n    },\r\n    submitBatchUpdate() {\r\n      if (this.batchUploadList.length === 0) return this.$message.warning('没有可提交的数据')\r\n\r\n      // 过滤出没有错误的数据\r\n      const validData = this.batchUploadList.filter(item => !item.rowMsg)\r\n      if (validData.length === 0) {\r\n        return this.$message.warning('没有有效的数据可以提交，请先删除错误行或修正数据')\r\n      }\r\n\r\n      const errorCount = this.getBatchErrorCount()\r\n      if (errorCount > 0) {\r\n        this.$confirm(`当前有 ${errorCount} 条错误数据将被忽略，只提交 ${validData.length} 条有效数据，是否继续？`, '提示', {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning'\r\n        }).then(() => {\r\n          this.checkDuplicatesAndUpdate(validData)\r\n        }).catch(() => {})\r\n      } else {\r\n        this.checkAndConfirmDuplicates(validData)\r\n      }\r\n    },\r\n    checkAndConfirmDuplicates(data) {\r\n      // 检查重复编码\r\n      this.$http({ url: '/am/location/checkBatchDuplicates', data: data }).then(res => {\r\n        if (res.code === 1 && res.data) {\r\n          const duplicateInfo = res.data\r\n          if (duplicateInfo.hasDuplicates) {\r\n            let message = ''\r\n            const importDuplicates = duplicateInfo.importDuplicates || []\r\n            const dbDuplicates = duplicateInfo.dbDuplicates || []\r\n\r\n            if (importDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在导入数据中重复出现：${importDuplicates.join(', ')}。\\n`\r\n            }\r\n\r\n            if (dbDuplicates.length > 0) {\r\n              message += `检测到以下网点编码在数据库中存在多条记录：${dbDuplicates.join(', ')}。\\n`\r\n            }\r\n            message += `重复编码将以最后一条数据为准\\n`\r\n            message += `是否继续执行批量更新？`\r\n\r\n            this.$confirm(message, '发现重复编码', {\r\n              confirmButtonText: '确定更新',\r\n              cancelButtonText: '取消',\r\n              type: 'warning',\r\n              dangerouslyUseHTMLString: false\r\n            }).then(() => {\r\n              this.doBatchUpdate(data)\r\n            }).catch(() => {\r\n              // 用户取消，不执行更新\r\n            })\r\n          } else {\r\n            // 没有重复编码，直接更新\r\n            this.doBatchUpdate(data)\r\n          }\r\n        } else {\r\n          // 检查失败，直接更新\r\n          this.doBatchUpdate(data)\r\n        }\r\n      }).catch(() => {\r\n        // 检查失败，直接更新\r\n        this.doBatchUpdate(data)\r\n      })\r\n    },\r\n    doBatchUpdate(data) {\r\n      const loadInst = Loading.service({ fullscreen: true, text: '数据上传中...' })\r\n      this.$http({ url: '/am/location/batchUpdate', data: data }).then(res => {\r\n        if (res.code === 1) {\r\n          this.$message.success(`批量更新成功`)\r\n          this.batchUploadVisible = false\r\n          this.searchLocation()\r\n        } else if (res.code === 2) {\r\n          this.batchUploadList = res.data\r\n          this.$message.error('部分数据更新失败，请查看错误信息')\r\n        }\r\n        loadInst.close()\r\n      }).catch(() => {\r\n        loadInst.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang='scss' scope>\r\n.layout-lr {\r\n  height: calc(100vh - 52px);\r\n}\r\n\r\n.layout-lr .left {\r\n  min-width: 220px;\r\n}\r\n\r\n.layout-lr .center {\r\n  overflow: hidden;\r\n}\r\n\r\n.location-head {\r\n  height: 40px;\r\n}\r\n\r\n.location-title {\r\n  line-height: 32px;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: #303133;\r\n}\r\n\r\n.location-search {\r\n  width: 300px;\r\n  float: right;\r\n}\r\n\r\n.upload-block {\r\n  padding: 0 4px;\r\n  width: calc(100vw - 12px);\r\n  height: calc(100vh - 250px);\r\n  overflow: auto;\r\n}\r\n\r\n.upload-block table {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n}\r\n\r\n.upload-block table th,\r\n.upload-block table td {\r\n  border: 1px solid #cccccc;\r\n  border-collapse: collapse;\r\n  padding: 4px 6px;\r\n  font-size: 12px;\r\n  line-height: 18px;\r\n}\r\n\r\n.upload-block table tr.err {\r\n  background-color: #faee92;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SA,SAAAA,OAAA;AACA,OAAAC,SAAA;AACA,OAAAC,MAAA;AACA,OAAAC,OAAA;AACA,OAAAC,UAAA;AACA,OAAAC,WAAA;AACA,OAAAC,UAAA;AAEA;EACAC,UAAA;IAAAN,SAAA,EAAAA,SAAA;IAAAC,MAAA,EAAAA,MAAA;IAAAC,OAAA,EAAAA,OAAA;IAAAC,UAAA,EAAAA,UAAA;IAAAC,WAAA,EAAAA,WAAA;IAAAC,UAAA,EAAAA;EAAA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,QAAA;MAAA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,UAAA;QAAAC,MAAA;MAAA;MACAC,WAAA;QACAC,IAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,IAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAE,IAAA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAL,MAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAG,KAAA;QAAAC,OAAA;MAAA;MACAC,eAAA;MACAC,YAAA;QAAAX,MAAA;MAAA;MACAY,aAAA;QACAV,IAAA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAC,IAAA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAQ,QAAA;MACAC,UAAA;MACAC,aAAA;MACAC,aAAA;MACAC,eAAA;MACA;MACAC,aAAA;MACAC,eAAA;MACAC,kBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,UAAA;IACA,KAAAC,cAAA;IACA,KAAAC,KAAA,CAAAC,IAAA,CAAAC,YAAA,CAAAC,IAAA,CAAAC,GAAA,CAAAC,QAAA,CAAAC,eAAA,CAAAC,YAAA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAAA,QAAA;MACA,IAAAC,IAAA,OAAAC,IAAA,CAAAF,QAAA;MACA,IAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,IAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,IAAAO,OAAA,GAAAT,MAAA,CAAAL,IAAA,CAAAe,UAAA,IAAAR,QAAA;MACA,UAAAS,MAAA,CAAAd,IAAA,OAAAc,MAAA,CAAAZ,KAAA,OAAAY,MAAA,CAAAR,GAAA,OAAAQ,MAAA,CAAAN,KAAA,OAAAM,MAAA,CAAAJ,OAAA,OAAAI,MAAA,CAAAF,OAAA;IACA;IACA5B,YAAA,WAAAA,aAAA;MAAA,IAAA+B,KAAA;MACA,KAAAC,KAAA,2BAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAA1D,QAAA,GAAA6D,GAAA;MACA,GAAAC,KAAA;QAAAJ,KAAA,CAAAK,MAAA;MAAA;IACA;IACAnC,UAAA,WAAAA,WAAA;MAAA,IAAAoC,MAAA;MACA,KAAAL,KAAA,oBAAAC,IAAA,WAAAC,GAAA;QACAG,MAAA,CAAAjE,UAAA,GAAA8D,GAAA;QACA,IAAAG,MAAA,CAAA/D,UAAA,IAAA+D,MAAA,CAAA/D,UAAA,CAAAgE,EAAA;UACA,SAAAC,CAAA,MAAAA,CAAA,GAAAL,GAAA,CAAAM,MAAA,EAAAD,CAAA;YACA,IAAAL,GAAA,CAAAK,CAAA,EAAAD,EAAA,KAAAD,MAAA,CAAA/D,UAAA,CAAAgE,EAAA;cACAD,MAAA,CAAA/D,UAAA,GAAA4D,GAAA,CAAAK,CAAA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAE,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAApE,UAAA,GAAAoE,IAAA;MACA,KAAAxD,KAAA,CAAAR,MAAA,GAAAgE,IAAA,CAAAJ,EAAA;MACA,KAAApD,KAAA,CAAAC,OAAA;MACA,KAAAe,cAAA;IACA;IACAyC,SAAA,WAAAA,UAAA;MACA,KAAAnE,aAAA;MACA,KAAAC,UAAA;QAAAmE,GAAA;MAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,UAAAvE,UAAA,CAAAgE,EAAA;MACA,KAAA9D,aAAA;MACA,IAAAsE,IAAA,GAAAC,IAAA,CAAAC,SAAA,MAAA1E,UAAA;MACA,KAAAG,UAAA,GAAAsE,IAAA,CAAAE,KAAA,CAAAH,IAAA;IACA;IACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA,CAAAiD,UAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAnB,KAAA;YAAAuB,GAAA;YAAApF,IAAA,EAAAgF,MAAA,CAAA1E;UAAA,GAAAwD,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAtD,IAAA;cACAuE,MAAA,CAAAK,QAAA,CAAAC,OAAA;cACAN,MAAA,CAAA3E,aAAA;cACA2E,MAAA,CAAAlD,UAAA;YACA;UACA;QACA;MACA;IACA;IACAyD,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,IAAA;MAAA,GAAA9B,IAAA;QACA0B,MAAA,CAAA3B,KAAA;UAAAuB,GAAA,yBAAAI,MAAA,CAAArF,UAAA,CAAAgE;QAAA,GAAAL,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACA+E,MAAA,CAAAH,QAAA,CAAAC,OAAA;YACAE,MAAA,CAAArF,UAAA;YACAqF,MAAA,CAAA1D,UAAA;UACA;QACA;MACA,GAAAkC,KAAA;IACA;IACAjC,cAAA,WAAAA,eAAA;MACA,KAAAC,KAAA,CAAAC,IAAA,CAAA4D,MAAA,MAAA9E,KAAA;IACA;IACA+E,WAAA,WAAAA,YAAA;MACA,KAAA7E,eAAA;MACA,KAAAC,YAAA;QAAAX,MAAA,OAAAJ,UAAA,CAAAgE;MAAA;MACA,KAAA3C,eAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACAuE,YAAA,WAAAA,aAAAxB,IAAA;MAAA,IAAAyB,MAAA;MACA,KAAAnC,KAAA,uBAAAU,IAAA,CAAAJ,EAAA,EAAAL,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA,QAAAsD,GAAA,CAAA/D,IAAA;UACAgG,MAAA,CAAA/E,eAAA;UACA+E,MAAA,CAAA9E,YAAA,GAAA6C,GAAA,CAAA/D,IAAA;UACAgG,MAAA,CAAAxE,eAAA,GAAAuC,GAAA,CAAA/D,IAAA,CAAAwB,eAAA;QACA;MACA;IACA;IACAyE,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAlE,KAAA,CAAAmE,YAAA,CAAAjB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAiB,OAAA;UACAF,MAAA,CAAA1E,eAAA,CAAA6E,OAAA,WAAAC,CAAA;YAAA,OAAAF,OAAA,CAAAG,IAAA;cAAAC,EAAA,EAAAF,CAAA,CAAAE;YAAA;UAAA;UACA,KAAAJ,OAAA,CAAA/B,MAAA,SAAA6B,MAAA,CAAAb,QAAA,CAAAoB,OAAA;UACAP,MAAA,CAAAhF,YAAA,CAAAM,eAAA,GAAA4E,OAAA;UACAF,MAAA,CAAArC,KAAA;YAAAuB,GAAA;YAAApF,IAAA,EAAAkG,MAAA,CAAAhF;UAAA,GAAA4C,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAtD,IAAA;cACAyF,MAAA,CAAAb,QAAA,CAAAC,OAAA;cACAY,MAAA,CAAAjF,eAAA;cACAiF,MAAA,CAAAnE,cAAA;YACA;UACA;QACA;MACA;IACA;IACA2E,cAAA,WAAAA,eAAAnC,IAAA;MAAA,IAAAoC,MAAA;MACA,KAAAlB,QAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,IAAA;MAAA,GAAA9B,IAAA;QACA6C,MAAA,CAAA9C,KAAA;UAAAuB,GAAA,2BAAAb,IAAA,CAAAJ;QAAA,GAAAL,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACAkG,MAAA,CAAAtB,QAAA,CAAAC,OAAA;YACAqB,MAAA,CAAA5E,cAAA;UACA;QACA;MACA,GAAAiC,KAAA;IACA;IACA4C,MAAA,WAAAA,OAAA;MACA,IAAAC,EAAA,QAAA3F,YAAA,CAAA4F,GAAA;QAAAC,GAAA,OAAA7F,YAAA,CAAA6F,GAAA;QAAAD,GAAA,OAAA5F,YAAA,CAAA4F;MAAA;MACA,KAAA9E,KAAA,CAAAgF,WAAA,CAAAC,IAAA,CAAAJ,EAAA;IACA;IACAK,KAAA,WAAAA,MAAAZ,CAAA;MACA,KAAAa,IAAA,MAAAjG,YAAA,aAAAoF,CAAA,CAAAc,OAAA;MACA,KAAAD,IAAA,MAAAjG,YAAA,SAAAoF,CAAA,CAAAe,MAAA,GAAAf,CAAA,CAAAe,MAAA,CAAAN,GAAA;MACA,KAAAI,IAAA,MAAAjG,YAAA,SAAAoF,CAAA,CAAAe,MAAA,GAAAf,CAAA,CAAAe,MAAA,CAAAP,GAAA;IACA;IACAQ,MAAA,WAAAA,OAAA;MACA,KAAAlG,QAAA;MACA,KAAAC,UAAA;MACA,KAAAC,aAAA;IACA;IACAiG,YAAA,WAAAA,aAAA;MACA,KAAAlG,UAAA;IACA;IACAmG,SAAA,WAAAA,UAAA;MACA,KAAAjG,aAAA,SAAAA,aAAA;IACA;IACAkG,QAAA,WAAAA,SAAArG,QAAA;MAAA,IAAAsG,MAAA;MACA,IAAAtG,QAAA,IAAAA,QAAA,CAAAiD,MAAA;QACA,IAAAsD,QAAA,GAAAnI,OAAA,CAAAoI,OAAA;UAAAC,UAAA;UAAAC,IAAA;QAAA;QACA,KAAAjE,KAAA;UAAAuB,GAAA;UAAApF,IAAA,EAAAoB,QAAA;QAAA,GAAA0C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACAiH,MAAA,CAAAnG,aAAA;YACAmG,MAAA,CAAArG,UAAA,GAAA0C,GAAA,CAAA/D,IAAA;UACA;UACA2H,QAAA,CAAAI,KAAA;QACA;MACA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAA5G,UAAA,CAAAgD,MAAA,oBAAAgB,QAAA,CAAAoB,OAAA;MACA,IAAAkB,QAAA,GAAAnI,OAAA,CAAAoI,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAjE,KAAA;QAAAuB,GAAA;QAAApF,IAAA,OAAAqB;MAAA,GAAAyC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA;UACAwH,MAAA,CAAA5C,QAAA,CAAAC,OAAA;UACA2C,MAAA,CAAAC,KAAA;UACAD,MAAA,CAAA3G,aAAA;UACA2G,MAAA,CAAApC,MAAA;QACA,WAAA9B,GAAA,CAAAtD,IAAA;UACAwH,MAAA,CAAA5G,UAAA,GAAA0C,GAAA,CAAA/D,IAAA;UACAiI,MAAA,CAAA5C,QAAA,CAAA8C,KAAA;QACA;QACAR,QAAA,CAAAI,KAAA;MACA,GAAA/D,KAAA;QACA2D,QAAA,CAAAI,KAAA;QACA;MACA;IACA;IACAK,QAAA,WAAAA,SAAA;MACA,KAAA5G,eAAA,CAAA+E,IAAA;IACA;IACA8B,WAAA,WAAAA,YAAAC,QAAA;MACA,KAAA9G,eAAA,CAAA+G,MAAA,CAAAD,QAAA;IACA;IACA;IACAE,kBAAA,WAAAA,mBAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAC,WAAA;MACA,WAAAD,OAAA;QACA,KAAAE,WAAA;MACA;IACA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MACA,UAAAzI,UAAA,UAAAA,UAAA,CAAAgE,EAAA;QACA,YAAAkB,QAAA,CAAAoB,OAAA;MACA;MACA,IAAAkB,QAAA,GAAAnI,OAAA,CAAAoI,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAe,OAAA;QAAAzD,GAAA,8BAAAzB,MAAA,MAAAxD,UAAA,CAAAgE,EAAA;QAAA2E,YAAA;MAAA,GAAAhF,IAAA,WAAAiF,IAAA;QACApB,QAAA,CAAAI,KAAA;QACAa,MAAA,CAAAI,OAAA,CAAAD,IAAA;MACA,GAAA/E,KAAA,WAAAiF,GAAA;QACAtB,QAAA,CAAAI,KAAA;QACAa,MAAA,CAAAvD,QAAA,CAAA8C,KAAA,aAAAc,GAAA;MACA;IACA;IACAN,WAAA,WAAAA,YAAA;MACA,UAAAxI,UAAA,UAAAA,UAAA,CAAAgE,EAAA;QACA,YAAAkB,QAAA,CAAAoB,OAAA;MACA;MACA,KAAAhF,aAAA;MACA,KAAAC,eAAA;MACA,KAAAC,kBAAA;IACA;IACAuH,iBAAA,WAAAA,kBAAA;MACA,KAAAxH,eAAA;IACA;IACAyH,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,KAAA5D,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA9B,IAAA;QACAuF,MAAA,CAAA3H,eAAA,CAAA6G,MAAA,CAAAa,KAAA;QACAC,MAAA,CAAAhE,QAAA,CAAAC,OAAA;MACA,GAAAtB,KAAA;IACA;IACAsF,kBAAA,WAAAA,mBAAA;MACA,YAAA5H,eAAA,CAAA6H,MAAA,WAAAhF,IAAA;QAAA,OAAAA,IAAA,CAAAiF,MAAA;MAAA,GAAAnF,MAAA;IACA;IACAoF,aAAA,WAAAA,cAAArI,QAAA;MAAA,IAAAsI,OAAA;MACA,IAAAtI,QAAA,IAAAA,QAAA,CAAAiD,MAAA;QACA,IAAAsD,QAAA,GAAAnI,OAAA,CAAAoI,OAAA;UAAAC,UAAA;UAAAC,IAAA;QAAA;QACA,KAAAjE,KAAA;UAAAuB,GAAA;UAAApF,IAAA,EAAAoB,QAAA;QAAA,GAAA0C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAtD,IAAA;YACAiJ,OAAA,CAAAhI,eAAA,GAAAqC,GAAA,CAAA/D,IAAA;UACA;UACA2H,QAAA,CAAAI,KAAA;QACA;MACA;IACA;IACA4B,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,SAAAlI,eAAA,CAAA2C,MAAA,oBAAAgB,QAAA,CAAAoB,OAAA;;MAEA;MACA,IAAAoD,SAAA,QAAAnI,eAAA,CAAA6H,MAAA,WAAAhF,IAAA;QAAA,QAAAA,IAAA,CAAAiF,MAAA;MAAA;MACA,IAAAK,SAAA,CAAAxF,MAAA;QACA,YAAAgB,QAAA,CAAAoB,OAAA;MACA;MAEA,IAAAqD,UAAA,QAAAR,kBAAA;MACA,IAAAQ,UAAA;QACA,KAAArE,QAAA,uBAAA9B,MAAA,CAAAmG,UAAA,sFAAAnG,MAAA,CAAAkG,SAAA,CAAAxF,MAAA;UACAqB,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAA9B,IAAA;UACA8F,OAAA,CAAAG,wBAAA,CAAAF,SAAA;QACA,GAAA7F,KAAA;MACA;QACA,KAAAgG,yBAAA,CAAAH,SAAA;MACA;IACA;IACAG,yBAAA,WAAAA,0BAAAhK,IAAA;MAAA,IAAAiK,OAAA;MACA;MACA,KAAApG,KAAA;QAAAuB,GAAA;QAAApF,IAAA,EAAAA;MAAA,GAAA8D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA,UAAAsD,GAAA,CAAA/D,IAAA;UACA,IAAAkK,aAAA,GAAAnG,GAAA,CAAA/D,IAAA;UACA,IAAAkK,aAAA,CAAAC,aAAA;YACA,IAAAxJ,OAAA;YACA,IAAAyJ,gBAAA,GAAAF,aAAA,CAAAE,gBAAA;YACA,IAAAC,YAAA,GAAAH,aAAA,CAAAG,YAAA;YAEA,IAAAD,gBAAA,CAAA/F,MAAA;cACA1D,OAAA,+HAAAgD,MAAA,CAAAyG,gBAAA,CAAAE,IAAA;YACA;YAEA,IAAAD,YAAA,CAAAhG,MAAA;cACA1D,OAAA,qIAAAgD,MAAA,CAAA0G,YAAA,CAAAC,IAAA;YACA;YACA3J,OAAA;YACAA,OAAA;YAEAsJ,OAAA,CAAAxE,QAAA,CAAA9E,OAAA;cACA+E,iBAAA;cACAC,gBAAA;cACAC,IAAA;cACA2E,wBAAA;YACA,GAAAzG,IAAA;cACAmG,OAAA,CAAAO,aAAA,CAAAxK,IAAA;YACA,GAAAgE,KAAA;cACA;YAAA,CACA;UACA;YACA;YACAiG,OAAA,CAAAO,aAAA,CAAAxK,IAAA;UACA;QACA;UACA;UACAiK,OAAA,CAAAO,aAAA,CAAAxK,IAAA;QACA;MACA,GAAAgE,KAAA;QACA;QACAiG,OAAA,CAAAO,aAAA,CAAAxK,IAAA;MACA;IACA;IACAwK,aAAA,WAAAA,cAAAxK,IAAA;MAAA,IAAAyK,OAAA;MACA,IAAA9C,QAAA,GAAAnI,OAAA,CAAAoI,OAAA;QAAAC,UAAA;QAAAC,IAAA;MAAA;MACA,KAAAjE,KAAA;QAAAuB,GAAA;QAAApF,IAAA,EAAAA;MAAA,GAAA8D,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAtD,IAAA;UACAgK,OAAA,CAAApF,QAAA,CAAAC,OAAA;UACAmF,OAAA,CAAA9I,kBAAA;UACA8I,OAAA,CAAA1I,cAAA;QACA,WAAAgC,GAAA,CAAAtD,IAAA;UACAgK,OAAA,CAAA/I,eAAA,GAAAqC,GAAA,CAAA/D,IAAA;UACAyK,OAAA,CAAApF,QAAA,CAAA8C,KAAA;QACA;QACAR,QAAA,CAAAI,KAAA;MACA,GAAA/D,KAAA;QACA2D,QAAA,CAAAI,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}