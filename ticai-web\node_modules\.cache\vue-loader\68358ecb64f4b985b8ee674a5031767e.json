{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\allocate\\apply.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\allocate\\apply.vue", "mtime": 1753352753268}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["apply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "apply.vue", "sourceRoot": "src/views/asset/allocate", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog v-dialog-drag title=\"调拨申请\" width=\"900px\" :visible.sync=\"visible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" size=\"small\" label-width=\"140px\" :model=\"form\" :rules=\"formRules\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"调拨申请日期：\" prop=\"time\">\r\n              <el-date-picker v-model=\"form.time\" type=\"date\" placeholder=\"请选择调拨日期\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" clearable editable style=\"width:200px;\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"调拨申请人：\" prop=\"time\">\r\n              <user-chosen v-model=\"form.user\" type=\"1\" clearable placeholder=\"请选择调拨申请人\" style=\"width:100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"调入部门/区域：\" prop=\"dept\">\r\n          <dept-region-chosen v-model=\"regions\" :simple=\"false\" clearable placeholder=\"请选择部门/区域\" style=\"width:100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"调拨说明：\" prop=\"memo\">\r\n          <el-input v-model=\"form.memo\" type=\"textarea\" maxlength=\"200\" show-word-limit clearable placeholder=\"请输入调拨说明\" style=\"width:100%\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <upload-file v-model=\"fileList\" simple multiple type=\"DB\" />\r\n      <div style=\"margin-top:20px;\">调拨资产明细：</div>\r\n      <el-divider></el-divider>\r\n      <el-table :data=\"assetList\" size=\"small\" border>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"180\" header-align=\"center\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"120\" align=\"center\" />\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"150\" />\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getAssetStatusType(scope.row)\" size=\"small\">{{ getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"80\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新 增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click.stop=\"removeAsset(scope.index)\">移除</el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"visible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <asset-chosen ref=\"asset\" multiple @selected=\"selectedAsset\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport store from '@/store'\r\nimport { dateStr } from '@/utils'\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nimport AssetChosen from '../account/AssetChosen.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport DeptRegionChosen from '@/views/components/DeptRegionChosen.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { AssetChosen, UserChosen, DeptRegionChosen, UploadFile },\r\n  data() {\r\n    const regionsRule = (rule, value, callback) => {\r\n      if (!this.form.dept) callback('请选择调拨部门/区域')\r\n      else callback()\r\n    }\r\n    return {\r\n      visible: false,\r\n      currDate: dateStr(),\r\n      form: { user: '' }, // 领用申请信息表单\r\n      regions: [],\r\n      formRules: {\r\n        time: [{ required: true, message: '请选择调拨日期', trigger: 'blur' }],\r\n        dept: [{ required: true, validator: regionsRule, trigger: 'blur' }]\r\n      },\r\n      assetList: [],\r\n      fileList: []\r\n    }\r\n  },\r\n  watch: {\r\n    regions: function(nv) {\r\n      this.form.dept = Array.isArray(nv) && nv.length > 0 ? nv[nv.length - 1] : null\r\n    }\r\n  },\r\n  methods: {\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    show() {\r\n      this.visible = true\r\n      this.form = { time: dateStr(), user: store.getters.user.id }\r\n      this.assetList = []\r\n      this.fileList = []\r\n      this.regions = []\r\n    },\r\n    addAsset() {\r\n      this.$refs.asset.show({ status: '1' })\r\n    },\r\n    selectedAsset(items) {\r\n      const ids = { }\r\n      this.assetList.forEach(r => { ids[r.id] = true })\r\n      items.forEach(r => {\r\n        if (!ids[r.id]) this.assetList.push(r)\r\n      })\r\n    },\r\n    removeAsset(index) {\r\n      this.$confirm('确定要移除吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.assetList.splice(index, 1)\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.assetList.forEach(r => details.push({ asset: r.id, dept: r.dept, region: r.region, location: r.location, useDept: r.userDept, useUser: r.useUser, assetStatus: r.status }))\r\n          if (!details.length) return this.$message.warning('请选择要调拨的资产')\r\n          this.form.assetList = details\r\n          this.form.fileList = this.fileList\r\n          this.$http({ url: '/am/asset/allocate/apply', data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.visible = false\r\n              this.$message.success('提交成功')\r\n              this.$emit('success')\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}