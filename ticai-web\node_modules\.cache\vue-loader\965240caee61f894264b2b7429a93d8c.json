{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backApply.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\asset\\back\\backApply.vue", "mtime": 1753352753269}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["backApply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "backApply.vue", "sourceRoot": "src/views/asset/back", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog v-dialog-drag title=\"退库申请\" width=\"900px\" :visible.sync=\"visible\" :close-on-press-escape=\"false\" :close-on-click-modal=\"false\">\r\n      <el-form ref=\"dataform\" size=\"small\" label-width=\"140px\" :model=\"form\" :rules=\"formRules\">\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"退库日期：\" prop=\"time\">\r\n              <el-date-picker v-model=\"form.time\" type=\"date\" placeholder=\"请选择退库日期\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" clearable editable style=\"width:100%;\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"退库人：\" prop=\"user\">\r\n              <user-chosen v-model=\"form.user\" type=\"1\" clearable placeholder=\"请选择退库人\" style=\"width:100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"10\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"退至部门/区域：\" prop=\"region\">\r\n              <dept-region-chosen v-model=\"deptRegion\" :simple=\"false\" clearable placeholder=\"请选择退至部门/区域\" style=\"width:100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"退库说明：\" prop=\"memo\">\r\n              <el-input v-model=\"form.memo\" maxlength=\"200\" show-word-limit clearable placeholder=\"请输入退库说明\" style=\"width:100%\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <upload-file v-model=\"fileList\" simple multiple type=\"TK\" />\r\n      <div style=\"margin-top:20px;\">退库资产明细：</div>\r\n      <el-divider></el-divider>\r\n      <el-table :data=\"assetList\" size=\"small\" border>\r\n        <el-table-column label=\"资产类型\" prop=\"typeName\" width=\"180\" header-align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"资产编码\" prop=\"no\" width=\"120\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"资产名称\" prop=\"name\" min-width=\"150\" fixed=\"left\" />\r\n        <el-table-column label=\"所属部门\" prop=\"deptName\" width=\"150\" header-align=\"center\" />\r\n        <el-table-column label=\"当前状态\" prop=\"status\" width=\"80\" align=\"center\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag :type=\"getAssetStatusType(scope.row)\" size=\"small\">{{ getAssetStatusText(scope.row) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"80\">\r\n          <template slot=\"header\">\r\n            <el-button type=\"success\" size=\"mini\" @click=\"addAsset\">新 增</el-button>\r\n          </template>\r\n          <template slot-scope=\"scope\">\r\n            <el-link type=\"danger\" size=\"mini\" icon=\"el-icon-remove\" @click.stop=\"removeAsset(scope.index)\">移除</el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"visible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"save\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    <asset-chosen ref=\"asset\" multiple @selected=\"selectedAsset\" />\r\n  </div>\r\n</template>\r\n<script>\r\nimport store from '@/store'\r\nimport { dateStr } from '@/utils'\r\nimport { getAssetStatusType, getAssetStatusText } from '../js/asset.js'\r\n\r\nimport AssetChosen from '../account/AssetChosen.vue'\r\nimport UserChosen from '@/views/components/UserChosen.vue'\r\nimport DeptRegionChosen from '@/views/components/DeptRegionChosen.vue'\r\nimport UploadFile from '@/views/components/UploadFile.vue'\r\n\r\nexport default {\r\n  components: { AssetChosen, UserChosen, DeptRegionChosen, UploadFile },\r\n  data() {\r\n    const deptRegionRule = (rule, value, callback) => {\r\n      if (!this.form.dept) callback('请选择退至部门/区域')\r\n      else callback()\r\n    }\r\n    return {\r\n      visible: false,\r\n      currDate: dateStr(),\r\n      form: { user: null }, // 退库申请信息表单\r\n      deptRegion: [],\r\n      formRules: {\r\n        time: [{ required: true, message: '请选择退库日期', trigger: 'blur' }],\r\n        user: [{ required: true, message: '请选择退库人', trigger: 'blur' }],\r\n        region: [{ validator: deptRegionRule, trigger: 'blur' }]\r\n      },\r\n      assetList: [],\r\n      fileList: []\r\n    }\r\n  },\r\n  watch: {\r\n    deptRegion: function(nv) {\r\n      this.form.dept = Array.isArray(nv) && nv.length > 0 ? nv[nv.length - 1] : null\r\n    }\r\n  },\r\n  methods: {\r\n    getAssetStatusType(v) {\r\n      return getAssetStatusType(v.status)\r\n    },\r\n    getAssetStatusText(v) {\r\n      return getAssetStatusText(v.status)\r\n    },\r\n    show() {\r\n      this.visible = true\r\n      this.form = { time: dateStr(), user: store.getters.user.id }\r\n      this.assetList = []\r\n      this.fileList = []\r\n    },\r\n    addAsset() {\r\n      this.$refs.asset.show({ status: '2' })\r\n    },\r\n    selectedAsset(items) {\r\n      const ids = { }\r\n      this.assetList.forEach(r => { ids[r.id] = true })\r\n      items.forEach(r => {\r\n        if (!ids[r.id]) this.assetList.push(r)\r\n      })\r\n    },\r\n    removeAsset(index) {\r\n      this.$confirm('确定要移除吗?', '提示', { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n        this.assetList.splice(index, 1)\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    save() {\r\n      this.$refs.dataform.validate(valid => {\r\n        if (valid) {\r\n          const details = []\r\n          this.assetList.forEach(r => details.push({ asset: r.id, dept: r.dept, region: r.region, location: r.location, useDept: r.useDept, useUser: r.useUser, assetStatus: r.status, lastLng: r.lng, lastLat: r.lat, lastLocAddr: r.locAddr }))\r\n          if (!details.length) return this.$message.warning('请选择要退库的资产')\r\n          this.form.assetList = details\r\n          this.form.fileList = this.fileList\r\n          this.$http({ url: '/am/asset/back/apply', data: this.form }).then(res => {\r\n            if (res.code > 0) {\r\n              this.visible = false\r\n              this.$message.success('提交成功')\r\n              this.$emit('success')\r\n            }\r\n          })\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}