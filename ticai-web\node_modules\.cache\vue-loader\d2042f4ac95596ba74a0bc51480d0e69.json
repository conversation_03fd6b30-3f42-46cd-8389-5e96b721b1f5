{"remainingRequest": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!F:\\work\\ticai\\ticai-web\\src\\views\\components\\PageTable.vue?vue&type=script&lang=js", "dependencies": [{"path": "F:\\work\\ticai\\ticai-web\\src\\views\\components\\PageTable.vue", "mtime": 1753352753271}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1747730941442}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1747730941451}, {"path": "F:\\work\\ticai\\ticai-web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1747730939034}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCByZXF1ZXN0IGZyb20gJy4uLy4uL3V0aWxzL3JlcXVlc3QnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1BhZ2VUYWJsZScsDQogIHByb3BzOiB7DQogICAgcGF0aDogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgcmVxdWlyZTogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9LA0KICAgIHBhZ2VTaXplOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAxMA0KICAgIH0sDQogICAgcGFnZVNpemVzOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFsxMCwgMjAsIDMwLCA1MCwgMTAwXQ0KICAgIH0sDQogICAgbGF5b3V0OiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnc2l6ZXMsIHByZXYsIHBhZ2VyLCBuZXh0LCBqdW1wZXInDQogICAgfSwNCiAgICBwYWdpbmc6IHsgLy8g5piv5ZCm5YiG6aG177yM6buY6K6k5Li6dHJ1Ze+8jOWNs+WIhumhteOAgu+8iOS4jeWIhumhteaXtuWwhuavj+mhteadoeaVsOiuvue9ruacgOWkp+OAgu+8iQ0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIHF1ZXJ5OiB7IC8vIOWIneWni+WMluWPguaVsA0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4ge30NCiAgICB9LA0KICAgIGF1dG86IHsgLy8g6Ieq5Yqo5p+l6K+iDQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGNoZWNrRmllbGQ6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9DQogICAgLy8gY29sdW1uczogew0KICAgIC8vICAgdHlwZTogQXJyYXksDQogICAgLy8gICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIC8vIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcGk6IDEsIC8vIOmhteaghw0KICAgICAgcHo6IHRoaXMucGFnZVNpemUsIC8vIOmhtemVvw0KICAgICAgcGFyYW1zOiB0aGlzLnF1ZXJ5IHx8IHt9LA0KICAgICAgcm93czogW10sDQogICAgICB0b3RhbDogMCwNCiAgICAgIGZyb206IDAsDQogICAgICB0bzogMCwNCiAgICAgIG1heEhlaWdodDogbnVsbCwNCiAgICAgIGxvYWRpbmc6IGZhbHNlDQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgIC8vIGNvbHVtblNlbGVjdGVkcyhuZXdBcnJheVZhbCkgew0KICAgIC8vICAgY29uc29sZS5sb2cobmV3QXJyYXlWYWwpDQogICAgLy8gICAvLyDorqHnrpfkuLrooqvpgInkuK3nmoTliJfmoIfpopjmlbDnu4QNCiAgICAvLyAgIHZhciBub25TZWxlY3RlZHMgPSB0aGlzLmNvbHVtbnMuZmlsdGVyKGl0ZW0gPT4gbmV3QXJyYXlWYWwuaW5kZXhPZihpdGVtLmluZGV4KSA9PT0gLTEpLm1hcChpdGVtID0+IGl0ZW0uaW5kZXgpDQogICAgLy8gICB0aGlzLmNvbHVtbnMuZmlsdGVyKGl0ZW0gPT4gew0KICAgIC8vICAgICBjb25zdCBpc05vblNlbGVjdGVkID0gbm9uU2VsZWN0ZWRzLmluZGV4T2YoaXRlbS5pbmRleCkgIT09IC0xDQogICAgLy8gICAgIGlmIChpc05vblNlbGVjdGVkKSB7DQogICAgLy8gICAgICAgLy8g6ZqQ6JeP5pyq6YCJ5Lit55qE5YiXDQogICAgLy8gICAgICAgaXRlbS52aXNpYmxlID0gZmFsc2UNCiAgICAvLyAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgLy8gICAgICAgICB0aGlzLiRyZWZzLmdyaWQuZG9MYXlvdXQoKQ0KICAgIC8vICAgICAgIH0pDQogICAgLy8gICAgIH0gZWxzZSB7DQogICAgLy8gICAgICAgLy8g5pi+56S65bey6YCJ5Lit55qE5YiXDQogICAgLy8gICAgICAgaXRlbS52aXNpYmxlID0gdHJ1ZQ0KICAgIC8vICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAvLyAgICAgICAgIHRoaXMuJHJlZnMuZ3JpZC5kb0xheW91dCgpDQogICAgLy8gICAgICAgfSkNCiAgICAvLyAgICAgfQ0KICAgIC8vICAgfSkNCiAgICAvLyB9DQogIH0sDQogIG1vdW50ZWQoKSB7DQogICAgaWYgKHRoaXMuYXV0bykgdGhpcy5zZWFyY2goKQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2V0UGFyYW1zKHZhbHVlKSB7DQogICAgICB0aGlzLnBhcmFtcyA9IHZhbHVlIHx8IHt9DQogICAgfSwNCiAgICBzZXRNYXhIZWlnaHQodmFsdWUpIHsNCiAgICAgIHRoaXMubWF4SGVpZ2h0ID0gdmFsdWUNCiAgICAgIHRoaXMuJHJlZnMuZ3JpZC5kb0xheW91dCgpDQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHZhbHVlKSB7DQogICAgICB0aGlzLnB6ID0gdmFsdWUNCiAgICAgIHRoaXMuc2VhcmNoKDEpDQogICAgfSwNCiAgICBoYW5kbGVOdW1iZXJDaGFuZ2UodmFsdWUpIHsNCiAgICAgIHRoaXMuc2VhcmNoKHZhbHVlKQ0KICAgIH0sDQogICAgc2VhcmNoKGFyZywgYSkgew0KICAgICAgaWYgKCF0aGlzLnBhdGgpIHJldHVybg0KICAgICAgY29uc3QgcHMgPSB7IHBhZ2VOdW1iZXI6IDEgfQ0KICAgICAgY29uc3QgYXJnVHlwZSA9IHR5cGVvZiBhcmcNCiAgICAgIGlmIChhcmdUeXBlID09PSAndW5kZWZpbmVkJykgcHMucGFnZU51bWJlciA9IDENCiAgICAgIGVsc2UgaWYgKGFyZ1R5cGUgPT09ICdudW1iZXInKSBwcy5wYWdlTnVtYmVyID0gYXJnDQogICAgICBlbHNlIGlmIChhcmdUeXBlID09PSAnb2JqZWN0Jykgew0KICAgICAgICB0aGlzLnBhcmFtcyA9IGFyZw0KICAgICAgICBpZiAodHlwZW9mIGEgPT09ICdudW1iZXInKSBwcy5wYWdlTnVtYmVyID0gYSAvLyDmjIflrprpobXnoIENCiAgICAgICAgaWYgKHR5cGVvZiBhID09PSAnYm9vbGVhbicpIHRoaXMuZW1wdHkoKSAvLyDmn6Xor6LliY3muIXnqboNCiAgICAgIH0gZWxzZSBwcy5wYWdlTnVtYmVyID0gYXJnLnBhZ2VOdW1iZXINCiAgICAgIHRoaXMucGkgPSBwcy5wYWdlTnVtYmVyDQogICAgICBpZiAodGhpcy5wYWdpbmcpIHsNCiAgICAgICAgdGhpcy5wYXJhbXMucGFnZU51bWJlciA9IHBzLnBhZ2VOdW1iZXINCiAgICAgICAgdGhpcy5wYXJhbXMucGFnZVNpemUgPSB0aGlzLnB6DQogICAgICB9DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICByZXF1ZXN0KHsNCiAgICAgICAgdXJsOiB0aGlzLnBhdGgsDQogICAgICAgIGRhdGE6IHRoaXMucGFyYW1zDQogICAgICB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAgIGlmICh0aGlzLnBhZ2luZykgdGhpcy5yZW5kZXJQYWdlKHJlcykNCiAgICAgICAgZWxzZSB0aGlzLnJlbmRlckxpc3QocmVzLnJvd3MgPyByZXMucm93cyA6IHJlcykNCiAgICAgICAgdGhpcy4kZW1pdCgnbG9hZGVkJywgcmVzKSAvLyDliqDovb3mlbDmja7ov5Tlm54NCiAgICAgIH0pLmNhdGNoKGVyciA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAgIGNvbnNvbGUubG9nKGVycikNCiAgICAgIH0pDQogICAgfSwNCiAgICBlbXB0eSgpIHsNCiAgICAgIHRoaXMucGkgPSAxDQogICAgICB0aGlzLnJvd3MgPSBbXQ0KICAgICAgdGhpcy50b3RhbCA9IDANCiAgICAgIHRoaXMuZnJvbSA9IDANCiAgICAgIHRoaXMudG8gPSAwDQogICAgfSwNCiAgICByZW5kZXJMaXN0KHJlcykgew0KICAgICAgdGhpcy5yb3dzID0gcmVzDQogICAgfSwNCiAgICByZW5kZXJQYWdlKHJlcykgew0KICAgICAgaWYgKHRoaXMuY2hlY2tGaWVsZCkgcmVzLnJvd3MuZm9yRWFjaChyID0+IHsgclt0aGlzLmNoZWNrRmllbGRdID0gZmFsc2UgfSkNCiAgICAgIHRoaXMucm93cyA9IHJlcy5yb3dzDQogICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsDQogICAgICBpZiAodGhpcy50b3RhbCA+IDApIHsNCiAgICAgICAgdGhpcy5mcm9tID0gKHRoaXMucGkgLSAxKSAqIHRoaXMucHogKyAxDQogICAgICAgIHRoaXMudG8gPSB0aGlzLmZyb20gKyAodGhpcy5yb3dzICYmIHRoaXMucm93cy5sZW5ndGggPyB0aGlzLnJvd3MubGVuZ3RoIC0gMSA6IDApDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZyb20gPSAwDQogICAgICAgIHRoaXMudG8gPSAwDQogICAgICB9DQogICAgfSwNCiAgICBnZXREYXRhKCkgew0KICAgICAgcmV0dXJuIHRoaXMucm93cw0KICAgIH0sDQogICAgZ2V0U2VsZWN0aW9uKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHJlZnMuZ3JpZC5zZWxlY3Rpb24NCiAgICB9LA0KICAgIGdldFNlbGVjdGlvbklkKGZpZWxkKSB7DQogICAgICBjb25zdCBpdGVtcyA9IHRoaXMuJHJlZnMuZ3JpZC5zZWxlY3Rpb24NCiAgICAgIGlmICghZmllbGQpIGZpZWxkID0gJ2lkJw0KICAgICAgY29uc3QgaWRzID0gW10NCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaXRlbXMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgaWYgKGl0ZW1zW2ldW2ZpZWxkXSkgaWRzLnB1c2goaXRlbXNbaV1bZmllbGRdKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGlkcw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["PageTable.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PageTable.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\r\n  <el-container class=\"page-table-ctn\">\r\n    <!-- <el-popover v-if=\"columns.length > 0\" width=\"60\" trigger=\"click\">\r\n      <el-checkbox-group v-model=\"columns\">\r\n        <el-checkbox v-for=\"item in columns\" :key=\"item.index\" :label=\"item.title\" />\r\n      </el-checkbox-group>\r\n      <el-button slot=\"reference\" icon=\"el-icon-more\" circle style=\"margin-left: 100px;\"></el-button>\r\n    </el-popover> -->\r\n    <el-table ref=\"grid\" v-loading=\"loading\" :max-height=\"maxHeight\" :data=\"rows\" v-bind=\"$attrs\" v-on=\"$listeners\">\r\n      <slot></slot>\r\n    </el-table>\r\n    <el-footer v-if=\"paging\" class=\"footer\">\r\n      <div class=\"size-info\">\r\n        <span v-if=\"total > 1\">显示第 {{ from }} 条到第 {{ to }} 条的数据，</span> 共{{ total }} 条数据\r\n      </div>\r\n      <el-pagination\r\n        style=\"float:right\"\r\n        :layout=\"layout\"\r\n        :page-sizes=\"pageSizes\"\r\n        :current-page=\"pi\"\r\n        :page-size=\"pz\"\r\n        :total=\"total\"\r\n        v-bind=\"$attrs\"\r\n        @current-change=\"handleNumberChange\"\r\n        @size-change=\"handleSizeChange\"\r\n      ></el-pagination>\r\n    </el-footer>\r\n  </el-container>\r\n</template>\r\n\r\n<script>\r\n\r\nimport request from '../../utils/request'\r\n\r\nexport default {\r\n  name: 'PageTable',\r\n  props: {\r\n    path: {\r\n      type: String,\r\n      require: true,\r\n      default: null\r\n    },\r\n    pageSize: {\r\n      type: Number,\r\n      default: 10\r\n    },\r\n    pageSizes: {\r\n      type: Array,\r\n      default: () => [10, 20, 30, 50, 100]\r\n    },\r\n    layout: {\r\n      type: String,\r\n      default: 'sizes, prev, pager, next, jumper'\r\n    },\r\n    paging: { // 是否分页，默认为true，即分页。（不分页时将每页条数设置最大。）\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    query: { // 初始化参数\r\n      type: Object,\r\n      default: () => {}\r\n    },\r\n    auto: { // 自动查询\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    checkField: {\r\n      type: String,\r\n      default: null\r\n    }\r\n    // columns: {\r\n    //   type: Array,\r\n    //   default: () => []\r\n    // }\r\n  },\r\n  data() {\r\n    return {\r\n      pi: 1, // 页标\r\n      pz: this.pageSize, // 页长\r\n      params: this.query || {},\r\n      rows: [],\r\n      total: 0,\r\n      from: 0,\r\n      to: 0,\r\n      maxHeight: null,\r\n      loading: false\r\n    }\r\n  },\r\n  watch: {\r\n    // columnSelecteds(newArrayVal) {\r\n    //   console.log(newArrayVal)\r\n    //   // 计算为被选中的列标题数组\r\n    //   var nonSelecteds = this.columns.filter(item => newArrayVal.indexOf(item.index) === -1).map(item => item.index)\r\n    //   this.columns.filter(item => {\r\n    //     const isNonSelected = nonSelecteds.indexOf(item.index) !== -1\r\n    //     if (isNonSelected) {\r\n    //       // 隐藏未选中的列\r\n    //       item.visible = false\r\n    //       this.$nextTick(() => {\r\n    //         this.$refs.grid.doLayout()\r\n    //       })\r\n    //     } else {\r\n    //       // 显示已选中的列\r\n    //       item.visible = true\r\n    //       this.$nextTick(() => {\r\n    //         this.$refs.grid.doLayout()\r\n    //       })\r\n    //     }\r\n    //   })\r\n    // }\r\n  },\r\n  mounted() {\r\n    if (this.auto) this.search()\r\n  },\r\n  methods: {\r\n    setParams(value) {\r\n      this.params = value || {}\r\n    },\r\n    setMaxHeight(value) {\r\n      this.maxHeight = value\r\n      this.$refs.grid.doLayout()\r\n    },\r\n    handleSizeChange(value) {\r\n      this.pz = value\r\n      this.search(1)\r\n    },\r\n    handleNumberChange(value) {\r\n      this.search(value)\r\n    },\r\n    search(arg, a) {\r\n      if (!this.path) return\r\n      const ps = { pageNumber: 1 }\r\n      const argType = typeof arg\r\n      if (argType === 'undefined') ps.pageNumber = 1\r\n      else if (argType === 'number') ps.pageNumber = arg\r\n      else if (argType === 'object') {\r\n        this.params = arg\r\n        if (typeof a === 'number') ps.pageNumber = a // 指定页码\r\n        if (typeof a === 'boolean') this.empty() // 查询前清空\r\n      } else ps.pageNumber = arg.pageNumber\r\n      this.pi = ps.pageNumber\r\n      if (this.paging) {\r\n        this.params.pageNumber = ps.pageNumber\r\n        this.params.pageSize = this.pz\r\n      }\r\n      this.loading = true\r\n      request({\r\n        url: this.path,\r\n        data: this.params\r\n      }).then(res => {\r\n        this.loading = false\r\n        if (this.paging) this.renderPage(res)\r\n        else this.renderList(res.rows ? res.rows : res)\r\n        this.$emit('loaded', res) // 加载数据返回\r\n      }).catch(err => {\r\n        this.loading = false\r\n        console.log(err)\r\n      })\r\n    },\r\n    empty() {\r\n      this.pi = 1\r\n      this.rows = []\r\n      this.total = 0\r\n      this.from = 0\r\n      this.to = 0\r\n    },\r\n    renderList(res) {\r\n      this.rows = res\r\n    },\r\n    renderPage(res) {\r\n      if (this.checkField) res.rows.forEach(r => { r[this.checkField] = false })\r\n      this.rows = res.rows\r\n      this.total = res.total\r\n      if (this.total > 0) {\r\n        this.from = (this.pi - 1) * this.pz + 1\r\n        this.to = this.from + (this.rows && this.rows.length ? this.rows.length - 1 : 0)\r\n      } else {\r\n        this.from = 0\r\n        this.to = 0\r\n      }\r\n    },\r\n    getData() {\r\n      return this.rows\r\n    },\r\n    getSelection() {\r\n      return this.$refs.grid.selection\r\n    },\r\n    getSelectionId(field) {\r\n      const items = this.$refs.grid.selection\r\n      if (!field) field = 'id'\r\n      const ids = []\r\n      for (let i = 0; i < items.length; i++) {\r\n        if (items[i][field]) ids.push(items[i][field])\r\n      }\r\n      return ids\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.page-table-ctn {\r\n  > .el-table {\r\n    width: '100%';\r\n    margin-bottom: 14px;\r\n    border: 1px solid #ebeef5;\r\n    border-bottom: unset;\r\n  }\r\n  > .footer {\r\n    height: 40px !important;\r\n    .size-info {\r\n      display: inline;\r\n      font-size: 12px;\r\n      color: #666666;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}